import { NotificationType } from '../entities/notification.entity';

export interface NotificationMessage {
  title: string;
  content: string;
  recipients: string[];
  config?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  tags?: string[];
}

export interface NotificationResult {
  success: boolean;
  messageId?: string;
  error?: string;
  details?: Record<string, any>;
}

export interface NotifierInterface {
  readonly type: NotificationType;
  
  /**
   * 发送通知
   */
  send(message: NotificationMessage): Promise<NotificationResult>;
  
  /**
   * 验证配置
   */
  validateConfig(config: Record<string, any>): boolean;
  
  /**
   * 测试连接
   */
  testConnection(config?: Record<string, any>): Promise<boolean>;
  
  /**
   * 获取支持的配置选项
   */
  getSupportedConfig(): Record<string, any>;
}

export interface EmailConfig {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject?: string;
  html?: boolean;
  attachments?: Array<{
    filename: string;
    content: string | Buffer;
    contentType?: string;
  }>;
}

export interface DingTalkConfig {
  webhook: string;
  secret?: string;
  atMobiles?: string[];
  atUserIds?: string[];
  isAtAll?: boolean;
}

export interface WeChatConfig {
  webhook: string;
  mentionedList?: string[];
  mentionedMobileList?: string[];
}

export interface SlackConfig {
  webhook: string;
  channel?: string;
  username?: string;
  iconEmoji?: string;
  iconUrl?: string;
}

export interface WebhookConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH';
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}
