-- DL引擎数据库初始化脚本
-- 创建所有微服务需要的数据库

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建服务注册中心数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_registry` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT 'DL引擎服务注册中心数据库';

-- 创建用户服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_users` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT 'DL引擎用户服务数据库';

-- 创建项目服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_projects` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT 'DL引擎项目服务数据库';

-- 创建资产服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_assets` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT 'DL引擎资产服务数据库';

-- 创建渲染服务数据库
CREATE DATABASE IF NOT EXISTS `dl_engine_render` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT 'DL引擎渲染服务数据库';

-- 创建监控服务数据库
CREATE DATABASE IF NOT EXISTS `monitoring` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT 'DL引擎监控服务数据库';

-- 显示创建的数据库
SHOW DATABASES LIKE '%engine%';
SHOW DATABASES LIKE 'monitoring';

-- 创建数据库用户（可选，用于权限分离）
-- 服务注册中心用户
CREATE USER IF NOT EXISTS 'registry_user'@'%' IDENTIFIED BY 'registry_password_2024';
GRANT ALL PRIVILEGES ON `dl_engine_registry`.* TO 'registry_user'@'%';

-- 用户服务用户
CREATE USER IF NOT EXISTS 'users_user'@'%' IDENTIFIED BY 'users_password_2024';
GRANT ALL PRIVILEGES ON `dl_engine_users`.* TO 'users_user'@'%';

-- 项目服务用户
CREATE USER IF NOT EXISTS 'projects_user'@'%' IDENTIFIED BY 'projects_password_2024';
GRANT ALL PRIVILEGES ON `dl_engine_projects`.* TO 'projects_user'@'%';

-- 资产服务用户
CREATE USER IF NOT EXISTS 'assets_user'@'%' IDENTIFIED BY 'assets_password_2024';
GRANT ALL PRIVILEGES ON `dl_engine_assets`.* TO 'assets_user'@'%';

-- 渲染服务用户
CREATE USER IF NOT EXISTS 'render_user'@'%' IDENTIFIED BY 'render_password_2024';
GRANT ALL PRIVILEGES ON `dl_engine_render`.* TO 'render_user'@'%';

-- 监控服务用户
CREATE USER IF NOT EXISTS 'monitoring_user'@'%' IDENTIFIED BY 'monitoring_password_2024';
GRANT ALL PRIVILEGES ON `monitoring`.* TO 'monitoring_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示用户信息
SELECT User, Host FROM mysql.user WHERE User LIKE '%_user';

-- 记录初始化完成
INSERT INTO mysql.general_log (event_time, user_host, thread_id, server_id, command_type, argument)
VALUES (NOW(), 'init_script', 0, 1, 'Query', 'DL Engine databases initialized successfully');

-- 输出初始化信息
SELECT 'DL引擎数据库初始化完成' AS status,
       NOW() AS timestamp,
       @@version AS mysql_version,
       @@character_set_server AS charset,
       @@collation_server AS collation;
