import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index } from 'typeorm';

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose',
}

@Entity('log_entries')
@Index(['level', 'timestamp'])
@Index(['service', 'timestamp'])
@Index(['timestamp'])
export class LogEntry {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: LogLevel,
  })
  level: LogLevel;

  @Column({ type: 'text' })
  message: string;

  @Column({ length: 100, nullable: true })
  service: string;

  @Column({ length: 100, nullable: true })
  module: string;

  @Column({ length: 50, nullable: true })
  requestId: string;

  @Column({ length: 50, nullable: true })
  userId: string;

  @Column({ type: 'json', nullable: true })
  context: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  stack: string;

  @Column({ length: 45, nullable: true })
  ip: string;

  @Column({ length: 500, nullable: true })
  userAgent: string;

  @CreateDateColumn()
  timestamp: Date;
}
