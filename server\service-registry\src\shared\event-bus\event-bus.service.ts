/**
 * 本地Event Bus服务实现
 */
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class EventBusService {
  private readonly logger = new Logger(EventBusService.name);

  constructor(private readonly eventEmitter: EventEmitter2) {}

  /**
   * 发布事件
   */
  async emit(event: string, payload: any): Promise<void> {
    try {
      this.logger.debug(`发布事件: ${event}`, payload);
      this.eventEmitter.emit(event, payload);
    } catch (error) {
      this.logger.error(`发布事件失败: ${event}`, error);
    }
  }

  /**
   * 发布事件（别名）
   */
  async publish(event: string, payload: any): Promise<void> {
    return this.emit(event, payload);
  }

  /**
   * 订阅事件
   */
  on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 订阅事件（别名）
   */
  subscribe(event: string, listener: (...args: any[]) => void): void {
    this.on(event, listener);
  }

  /**
   * 取消订阅
   */
  off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 一次性订阅
   */
  once(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.once(event, listener);
  }
}
