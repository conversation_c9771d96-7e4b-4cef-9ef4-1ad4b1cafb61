# ================================
# Service Registry 导入路径修复脚本
# PowerShell版本
# ================================

Write-Host "开始修复Service Registry导入路径..." -ForegroundColor Green

# 定义需要修复的文件和对应的替换规则
$files = @(
    "src\registry\cache\service-cache.service.ts",
    "src\registry\cache\service-cache.service.spec.ts", 
    "src\registry\enhanced-service-discovery.service.ts",
    "src\registry\registry.service.spec.ts",
    "src\registry\service-discovery.service.ts"
)

# 修复函数
function Fix-ImportPath {
    param(
        [string]$FilePath,
        [string]$OldPattern,
        [string]$NewPattern
    )
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        $originalContent = $content
        
        # 执行替换
        $content = $content -replace $OldPattern, $NewPattern
        
        if ($content -ne $originalContent) {
            Set-Content $FilePath $content -NoNewline
            Write-Host "已修复: $FilePath" -ForegroundColor Yellow
            return $true
        } else {
            Write-Host "无需修复: $FilePath" -ForegroundColor Gray
            return $false
        }
    } else {
        Write-Host "文件不存在: $FilePath" -ForegroundColor Red
        return $false
    }
}

# 修复所有文件
$fixedCount = 0

foreach ($file in $files) {
    Write-Host "检查文件: $file" -ForegroundColor Cyan
    
    # 修复 ../../../../shared/event-bus 导入
    if (Fix-ImportPath $file "../../../../shared/event-bus" "../../shared/event-bus") {
        $fixedCount++
    }
    
    # 修复 ../../../../shared/event-bus/events 导入
    if (Fix-ImportPath $file "../../../../shared/event-bus/events" "../../shared/event-bus/events") {
        $fixedCount++
    }
    
    # 修复 ../../../shared/event-bus 导入
    if (Fix-ImportPath $file "../../../shared/event-bus/enhanced-event-bus.service" "../shared/event-bus/enhanced-event-bus.service") {
        $fixedCount++
    }
    
    # 修复 ../../../shared/event-bus/events 导入
    if (Fix-ImportPath $file "../../../shared/event-bus/events" "../shared/event-bus/events") {
        $fixedCount++
    }
    
    # 修复 ../../../shared/event-bus 导入
    if (Fix-ImportPath $file "../../../shared/event-bus" "../shared/event-bus") {
        $fixedCount++
    }
}

Write-Host "修复完成! 共修复了 $fixedCount 个导入路径。" -ForegroundColor Green

# 验证修复结果
Write-Host "验证修复结果..." -ForegroundColor Cyan

$remainingIssues = 0
foreach ($file in $files) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        if ($content -match "../../../../shared|../../../shared") {
            Write-Host "警告: $file 仍包含旧的导入路径" -ForegroundColor Red
            $remainingIssues++
        }
    }
}

if ($remainingIssues -eq 0) {
    Write-Host "✅ 所有导入路径已修复!" -ForegroundColor Green
} else {
    Write-Host "❌ 仍有 $remainingIssues 个文件包含旧的导入路径" -ForegroundColor Red
}

Write-Host "修复脚本执行完成。" -ForegroundColor Green
