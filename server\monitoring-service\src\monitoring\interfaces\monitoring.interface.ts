export interface SystemMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
    cores: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  network: {
    bytesReceived: number;
    bytesSent: number;
    packetsReceived: number;
    packetsSent: number;
  };
  uptime: number;
  timestamp: Date;
}

export interface ServiceMetrics {
  serviceName: string;
  status: 'healthy' | 'unhealthy' | 'degraded' | 'unknown';
  responseTime: number;
  errorRate: number;
  requestCount: number;
  memoryUsage: number;
  cpuUsage: number;
  activeConnections: number;
  timestamp: Date;
}

export interface MetricPoint {
  name: string;
  value: number;
  labels?: Record<string, string>;
  timestamp?: Date;
  type?: 'gauge' | 'counter' | 'histogram' | 'summary';
}

export interface MetricQuery {
  name?: string;
  service?: string;
  startTime?: Date;
  endTime?: Date;
  labels?: Record<string, string>;
  limit?: number;
  offset?: number;
}

export interface MetricAggregation {
  name: string;
  aggregation: 'avg' | 'sum' | 'min' | 'max' | 'count';
  value: number;
  period: string;
  timestamp: Date;
}

export interface HealthCheckConfig {
  serviceName: string;
  url: string;
  method?: 'GET' | 'POST' | 'HEAD';
  timeout?: number;
  interval?: number;
  retries?: number;
  expectedStatus?: number;
  expectedBody?: string;
  headers?: Record<string, string>;
  enabled?: boolean;
}

export interface HealthCheckResult {
  serviceName: string;
  status: 'pass' | 'fail' | 'warn';
  responseTime: number;
  message?: string;
  error?: string;
  details?: Record<string, any>;
  timestamp: Date;
}
