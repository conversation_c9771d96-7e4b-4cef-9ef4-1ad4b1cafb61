# DL引擎MySQL服务安全指南

## 🔒 安全配置

### 1. 密码安全

#### 设置强密码
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，设置强密码
vim .env
```

**密码要求：**
- 至少12位字符
- 包含大小写字母、数字和特殊字符
- 避免使用常见密码或默认密码
- 定期更换密码

#### 密码示例
```bash
# 强密码示例（请勿直接使用）
MYSQL_ROOT_PASSWORD=DL#Engine2024@MySQL!Secure

# 生成随机密码的方法
openssl rand -base64 32
```

### 2. 环境变量验证

#### 启动前验证
```bash
# 验证环境变量配置
docker run --rm -v $(pwd)/.env:/app/.env mysql-service /usr/local/bin/validate-env.sh

# 或者在容器内验证
docker exec mysql-service /usr/local/bin/validate-env.sh
```

### 3. 网络安全

#### 限制网络访问
```yaml
# docker-compose.yml 中的网络配置
networks:
  dl-engine-network:
    driver: bridge
    internal: true  # 限制外部访问
```

#### 端口映射安全
```yaml
# 仅在必要时暴露端口
ports:
  - "127.0.0.1:3306:3306"  # 仅本地访问
  # - "3306:3306"          # 避免全网暴露
```

### 4. 数据加密

#### 启用SSL连接
```bash
# 在.env文件中启用SSL
ENABLE_SSL=true
SSL_CERT_PATH=/etc/mysql/ssl/server-cert.pem
SSL_KEY_PATH=/etc/mysql/ssl/server-key.pem
SSL_CA_PATH=/etc/mysql/ssl/ca-cert.pem
```

#### 数据卷加密
```bash
# 使用加密的数据卷
docker volume create --driver local \
  --opt type=tmpfs \
  --opt device=tmpfs \
  --opt o=size=1g,uid=999,gid=999 \
  mysql_encrypted_data
```

### 5. 访问控制

#### 用户权限分离
```sql
-- 为每个微服务创建专用用户
CREATE USER 'user_service'@'%' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON dl_engine_users.* TO 'user_service'@'%';

-- 限制root用户访问
CREATE USER 'admin'@'localhost' IDENTIFIED BY 'admin_password';
GRANT ALL PRIVILEGES ON *.* TO 'admin'@'localhost' WITH GRANT OPTION;
```

#### 禁用危险命令
```sql
-- 重命名危险命令
RENAME COMMAND FLUSHDB TO "";
RENAME COMMAND FLUSHALL TO "";
RENAME COMMAND SHUTDOWN TO "SHUTDOWN_DL_ENGINE";
```

### 6. 审计和监控

#### 启用审计日志
```cnf
# my.cnf 配置
[mysqld]
general_log = 1
general_log_file = /var/log/mysql/general.log
log_error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
```

#### 监控异常访问
```bash
# 监控失败的登录尝试
grep "Access denied" /var/log/mysql/error.log

# 监控慢查询
grep "Query_time" /var/log/mysql/slow.log
```

### 7. 备份安全

#### 加密备份
```bash
# 加密备份文件
docker exec mysql-service /usr/local/bin/backup.sh full
gpg --symmetric --cipher-algo AES256 backup_file.sql.gz
```

#### 备份验证
```bash
# 验证备份完整性
docker exec mysql-service /usr/local/bin/backup.sh verify
```

### 8. 容器安全

#### 非root用户运行
```dockerfile
# Dockerfile 中已配置
USER mysql
```

#### 安全选项
```yaml
# docker-compose.yml 安全配置
security_opt:
  - no-new-privileges:true
  - seccomp:unconfined
read_only: true
tmpfs:
  - /tmp
  - /var/run
```

### 9. 定期安全检查

#### 安全检查清单
- [ ] 密码强度检查
- [ ] 用户权限审计
- [ ] 网络访问控制
- [ ] 日志监控
- [ ] 备份验证
- [ ] 系统更新
- [ ] 漏洞扫描

#### 自动化安全检查
```bash
# 创建定期安全检查脚本
#!/bin/bash
# security-check.sh

# 检查密码强度
/usr/local/bin/validate-env.sh

# 检查用户权限
mysql -e "SELECT User, Host FROM mysql.user;"

# 检查失败登录
grep "Access denied" /var/log/mysql/error.log | tail -10

# 检查备份状态
/usr/local/bin/backup.sh status
```

### 10. 应急响应

#### 安全事件响应
```bash
# 1. 立即更改密码
mysql -u root -p -e "ALTER USER 'root'@'%' IDENTIFIED BY 'new_secure_password';"

# 2. 检查异常连接
mysql -e "SHOW PROCESSLIST;"

# 3. 查看访问日志
tail -f /var/log/mysql/general.log

# 4. 备份当前数据
/usr/local/bin/backup.sh full

# 5. 隔离容器
docker network disconnect dl-engine-network mysql-service
```

#### 数据恢复
```bash
# 从安全备份恢复
/usr/local/bin/restore.sh latest

# 验证数据完整性
mysql -e "CHECK TABLE database.table;"
```

## 🚨 安全警告

### 避免的做法
❌ 在Dockerfile中硬编码密码  
❌ 使用默认密码  
❌ 暴露MySQL端口到公网  
❌ 使用root用户进行应用连接  
❌ 禁用SSL连接  
❌ 忽略安全更新  

### 推荐的做法
✅ 使用环境变量传递密码  
✅ 定期更换密码  
✅ 限制网络访问  
✅ 使用专用用户连接  
✅ 启用SSL加密  
✅ 定期安全审计  

## 📞 安全支持

如发现安全问题，请立即：
1. 隔离受影响的服务
2. 查看审计日志
3. 备份当前数据
4. 联系安全团队
5. 按照应急响应流程处理

更多安全信息请参考DL引擎安全文档。
