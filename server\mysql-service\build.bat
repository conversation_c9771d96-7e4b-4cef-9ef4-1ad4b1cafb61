@echo off
REM DL引擎MySQL服务Windows构建脚本

setlocal enabledelayedexpansion

REM 配置变量
set IMAGE_NAME=dl-engine-mysql
set IMAGE_TAG=latest
set BUILD_CONTEXT=.
set DOCKERFILE=Dockerfile

echo [INFO] 开始DL引擎MySQL服务构建...

REM 检查Docker是否可用
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装或不可用
    exit /b 1
)

REM 检查必要文件
if not exist "%DOCKERFILE%" (
    echo [ERROR] Dockerfile不存在: %DOCKERFILE%
    exit /b 1
)

if not exist "config\my.cnf" (
    echo [ERROR] 配置文件不存在: config\my.cnf
    exit /b 1
)

if not exist ".env.example" (
    echo [ERROR] 环境变量示例文件不存在: .env.example
    exit /b 1
)

echo [INFO] 构建环境检查通过

REM 检查.env文件
if exist ".env" (
    echo [WARNING] 检测到.env文件，请确保它包含安全的密码配置
    echo [INFO] 请验证MYSQL_ROOT_PASSWORD已设置为强密码
    pause
)

REM 构建镜像
echo [INFO] 开始构建MySQL镜像...
echo [INFO] 镜像名称: %IMAGE_NAME%:%IMAGE_TAG%

docker build ^
    --file %DOCKERFILE% ^
    --tag %IMAGE_NAME%:%IMAGE_TAG% ^
    --label "com.dl-engine.service=mysql" ^
    --label "com.dl-engine.version=1.0.0" ^
    --label "com.dl-engine.build-date=%date% %time%" ^
    --label "com.dl-engine.description=DL引擎MySQL数据库服务" ^
    %BUILD_CONTEXT%

if errorlevel 1 (
    echo [ERROR] 镜像构建失败
    exit /b 1
)

echo [SUCCESS] 镜像构建成功: %IMAGE_NAME%:%IMAGE_TAG%

REM 显示镜像信息
echo.
echo ========================================
echo 构建完成信息:
echo ========================================
echo 镜像名称: %IMAGE_NAME%:%IMAGE_TAG%
echo 构建时间: %date% %time%

REM 获取镜像信息
for /f "tokens=3" %%i in ('docker images %IMAGE_NAME%:%IMAGE_TAG% --format "{{.ID}}"') do set IMAGE_ID=%%i
for /f "tokens=*" %%i in ('docker images %IMAGE_NAME%:%IMAGE_TAG% --format "{{.Size}}"') do set IMAGE_SIZE=%%i

echo 镜像ID: %IMAGE_ID%
echo 镜像大小: %IMAGE_SIZE%
echo.
echo 使用方法:
echo   docker run -d \
echo     --name dl-engine-mysql \
echo     -e MYSQL_ROOT_PASSWORD=your_secure_password \
echo     -p 3306:3306 \
echo     %IMAGE_NAME%:%IMAGE_TAG%
echo.
echo 或使用docker-compose:
echo   docker-compose up -d dl-engine-mysql
echo ========================================

REM 询问是否运行测试
set /p RUN_TEST="是否运行镜像测试? (y/N): "
if /i "%RUN_TEST%"=="y" (
    echo [INFO] 开始测试镜像...
    
    REM 创建测试容器
    set TEST_CONTAINER=test-%IMAGE_NAME%-%RANDOM%
    
    echo [INFO] 创建测试容器: !TEST_CONTAINER!
    
    docker run -d ^
        --name !TEST_CONTAINER! ^
        -e MYSQL_ROOT_PASSWORD=test_password_123 ^
        -e MYSQL_ALLOW_EMPTY_PASSWORD=no ^
        %IMAGE_NAME%:%IMAGE_TAG%
    
    if errorlevel 1 (
        echo [ERROR] 测试容器启动失败
        exit /b 1
    )
    
    echo [INFO] 等待MySQL服务启动...
    timeout /t 30 /nobreak >nul
    
    REM 测试健康检查
    docker exec !TEST_CONTAINER! /usr/local/bin/health-check.sh simple
    if errorlevel 1 (
        echo [ERROR] 健康检查测试失败
    ) else (
        echo [SUCCESS] 健康检查测试通过
    )
    
    REM 清理测试容器
    docker stop !TEST_CONTAINER! >nul 2>&1
    docker rm !TEST_CONTAINER! >nul 2>&1
    echo [SUCCESS] 测试容器已清理
)

echo [SUCCESS] MySQL服务构建完成

REM 显示安全提醒
echo.
echo ========================================
echo 安全提醒:
echo ========================================
echo 1. 请确保设置强密码（至少12位）
echo 2. 不要使用默认密码
echo 3. 生产环境建议定期更换密码
echo 4. 限制网络访问权限
echo 5. 启用SSL连接
echo 6. 定期备份数据
echo ========================================

pause
