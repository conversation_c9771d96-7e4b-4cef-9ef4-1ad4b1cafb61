# DL引擎Redis缓存服务 - 本地构建版本
# 使用本地可用的基础镜像

# 尝试使用本地已有的镜像，如果没有则使用最小的基础镜像
FROM alpine:3.18

# 维护者信息
LABEL maintainer="DL Engine Team"
LABEL description="DL引擎统一Redis缓存服务 - 本地构建版本"
LABEL version="1.0.0"

# 设置环境变量
ENV REDIS_USER=redis
ENV REDIS_HOME=/var/lib/redis

# 安装Redis和必要工具
RUN apk add --no-cache \
    redis \
    bash \
    curl \
    tzdata \
    procps \
    net-tools

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建Redis用户和目录
RUN addgroup -g 999 redis && \
    adduser -D -u 999 -G redis redis

# 创建必要的目录
RUN mkdir -p /usr/local/etc/redis \
             /var/lib/redis \
             /var/log/redis \
             /usr/local/bin/scripts

# 复制Redis配置文件
COPY config/redis.conf /usr/local/etc/redis/redis.conf

# 复制启动脚本
COPY scripts/start-redis.sh /usr/local/bin/start-redis.sh

# 复制健康检查脚本
COPY scripts/health-check.sh /usr/local/bin/health-check.sh

# 复制监控脚本
COPY scripts/monitor.sh /usr/local/bin/monitor.sh

# 复制备份脚本
COPY scripts/backup.sh /usr/local/bin/backup.sh

# 复制恢复脚本
COPY scripts/restore.sh /usr/local/bin/restore.sh

# 复制清理脚本
COPY scripts/cleanup.sh /usr/local/bin/cleanup.sh

# 设置脚本权限
RUN chmod +x /usr/local/bin/*.sh

# 设置目录权限
RUN chown -R redis:redis /var/lib/redis /var/log/redis /usr/local/etc/redis

# 创建数据卷挂载点
VOLUME ["/var/lib/redis", "/var/log/redis"]

# 暴露Redis端口
EXPOSE 6379

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD /usr/local/bin/health-check.sh simple || exit 1

# 切换到redis用户
USER redis

# 设置工作目录
WORKDIR /var/lib/redis

# 启动Redis服务
CMD ["/usr/local/bin/start-redis.sh"]
