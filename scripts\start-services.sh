#!/bin/bash

# DL引擎服务启动脚本
# 用于按正确顺序启动所有微服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker未运行，请先启动Docker"
        exit 1
    fi
    log_success "Docker运行正常"
}

# 检查Docker Compose是否可用
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    log_success "Docker Compose可用"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f .env ]; then
        log_warning ".env文件不存在，将创建默认配置"
        cat > .env << EOF
# 数据库配置
MYSQL_ROOT_PASSWORD=dl_engine_password_2024

# JWT配置
JWT_SECRET=dl_engine_jwt_secret_key_2024_very_secure

# 服务配置
NODE_ENV=production

# 网络配置
CORS_ORIGIN=*

# 文件上传配置
MAX_FILE_SIZE=104857600

# 监控配置
ELASTICSEARCH_ENABLED=true
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_mail_password
EOF
        log_success "已创建默认.env文件"
    else
        log_success ".env文件存在"
    fi
}

# 检查端口是否被占用
check_ports() {
    local ports=(80 3000 3001 3002 3003 3004 3005 3006 3007 3010 3030 3031 3100 3306 4001 4002 4003 4004 4010 6379 9090 9200 5601)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        log_warning "以下端口被占用: ${occupied_ports[*]}"
        log_warning "请确保这些端口可用，或停止占用这些端口的服务"
        read -p "是否继续启动？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_success "所有必需端口都可用"
    fi
}

# 等待服务健康检查
wait_for_health() {
    local service_name=$1
    local health_url=$2
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service_name 服务启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s $health_url > /dev/null 2>&1; then
            log_success "$service_name 服务启动成功"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    log_error "$service_name 服务启动超时"
    return 1
}

# 启动基础设施
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动MySQL和Redis
    docker-compose up -d mysql redis
    
    # 等待MySQL启动
    log_info "等待MySQL数据库启动..."
    sleep 15
    
    # 检查MySQL连接
    local mysql_ready=false
    for i in {1..30}; do
        if docker exec dl-engine-mysql mysqladmin ping -h localhost -u root -p${MYSQL_ROOT_PASSWORD:-dl_engine_password_2024} --silent 2>/dev/null; then
            mysql_ready=true
            break
        fi
        echo -n "."
        sleep 2
    done
    
    if [ "$mysql_ready" = true ]; then
        log_success "MySQL数据库启动成功"
    else
        log_error "MySQL数据库启动失败"
        return 1
    fi
    
    # 检查Redis连接
    if docker exec dl-engine-redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis缓存启动成功"
    else
        log_error "Redis缓存启动失败"
        return 1
    fi
}

# 启动服务注册中心
start_service_registry() {
    log_info "启动服务注册中心..."
    docker-compose up -d service-registry
    
    # 等待服务注册中心启动
    if wait_for_health "服务注册中心" "http://localhost:4010/health"; then
        return 0
    else
        return 1
    fi
}

# 启动业务微服务
start_business_services() {
    log_info "启动业务微服务..."
    docker-compose up -d user-service project-service asset-service render-service
    
    # 等待各个服务启动
    local services=(
        "用户服务:http://localhost:4001/health"
        "项目服务:http://localhost:4002/health"
        "资产服务:http://localhost:4003/health"
        "渲染服务:http://localhost:4004/health"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name url <<< "$service"
        if ! wait_for_health "$name" "$url"; then
            return 1
        fi
    done
}

# 启动协作服务
start_collaboration_services() {
    log_info "启动协作服务..."
    docker-compose up -d collaboration-service-1 collaboration-service-2
    
    # 等待协作服务实例启动
    sleep 10
    
    # 启动负载均衡器
    docker-compose up -d collaboration-load-balancer
    
    if wait_for_health "协作服务" "http://localhost:3007/health"; then
        return 0
    else
        return 1
    fi
}

# 启动API网关
start_api_gateway() {
    log_info "启动API网关..."
    docker-compose up -d api-gateway
    
    if wait_for_health "API网关" "http://localhost:3000/api/health"; then
        return 0
    else
        return 1
    fi
}

# 启动前端编辑器
start_editor() {
    log_info "启动前端编辑器..."
    docker-compose up -d editor
    
    # 等待编辑器启动
    sleep 10
    log_success "前端编辑器启动完成"
}

# 启动监控服务
start_monitoring() {
    if [ "$1" = "--with-monitoring" ]; then
        log_info "启动监控服务..."
        docker-compose -f docker-compose.monitoring.yml up -d
        log_success "监控服务启动完成"
    fi
}

# 显示服务状态
show_status() {
    log_info "服务状态检查..."
    docker-compose ps
    
    echo
    log_info "服务访问地址："
    echo "  前端编辑器: http://localhost"
    echo "  API网关: http://localhost:3000"
    echo "  API文档: http://localhost:3000/api/docs"
    echo "  服务注册中心: http://localhost:4010"
    echo "  Grafana监控: http://localhost:3000 (admin/admin)"
    echo "  Prometheus: http://localhost:9090"
    echo "  Kibana日志: http://localhost:5601"
}

# 主函数
main() {
    echo "=========================================="
    echo "    DL引擎服务启动脚本"
    echo "=========================================="
    
    # 检查前置条件
    check_docker
    check_docker_compose
    check_env_file
    check_ports
    
    echo
    log_info "开始启动DL引擎服务..."
    
    # 按顺序启动服务
    if start_infrastructure && \
       start_service_registry && \
       start_business_services && \
       start_collaboration_services && \
       start_api_gateway && \
       start_editor; then
        
        # 启动监控服务（可选）
        start_monitoring "$1"
        
        echo
        log_success "所有服务启动完成！"
        show_status
        
    else
        log_error "服务启动失败，请检查日志"
        echo
        log_info "查看服务日志："
        echo "  docker-compose logs [service-name]"
        exit 1
    fi
}

# 脚本入口
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  --with-monitoring    同时启动监控服务"
    echo "  --help, -h          显示帮助信息"
    exit 0
fi

main "$@"
