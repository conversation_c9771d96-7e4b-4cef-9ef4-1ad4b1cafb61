#!/bin/bash

# DL引擎MySQL服务安全构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 配置变量
IMAGE_NAME=${IMAGE_NAME:-"dl-engine-mysql"}
IMAGE_TAG=${IMAGE_TAG:-"latest"}
BUILD_CONTEXT=${BUILD_CONTEXT:-"."}
DOCKERFILE=${DOCKERFILE:-"Dockerfile"}

# 检查环境
check_environment() {
    log_info "检查构建环境..."
    
    # 检查Docker是否可用
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不可用"
        exit 1
    fi
    
    # 检查Dockerfile是否存在
    if [ ! -f "$DOCKERFILE" ]; then
        log_error "Dockerfile不存在: $DOCKERFILE"
        exit 1
    fi
    
    # 检查必要的文件
    local required_files=(
        "config/my.cnf"
        "init-scripts/01-create-databases.sql"
        "scripts/health-check.sh"
        "scripts/backup.sh"
        "scripts/restore.sh"
        "scripts/validate-env.sh"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "必需文件不存在: $file"
            exit 1
        fi
    done
    
    log_success "构建环境检查通过"
}

# 验证安全配置
validate_security() {
    log_info "验证安全配置..."
    
    # 检查Dockerfile中是否包含敏感信息
    if grep -q "MYSQL_ROOT_PASSWORD=" "$DOCKERFILE"; then
        local password_line=$(grep "MYSQL_ROOT_PASSWORD=" "$DOCKERFILE")
        if [[ "$password_line" =~ ENV.*MYSQL_ROOT_PASSWORD=.+ ]]; then
            log_error "Dockerfile中包含硬编码密码，这是安全风险"
            log_error "请移除: $password_line"
            exit 1
        fi
    fi
    
    # 检查是否存在.env文件（不应包含在镜像中）
    if [ -f ".env" ]; then
        log_warning "检测到.env文件，确保它在.dockerignore中"
        if [ -f ".dockerignore" ] && grep -q "\.env" ".dockerignore"; then
            log_success ".env文件已在.dockerignore中排除"
        else
            log_error ".env文件可能会被包含在镜像中，请添加到.dockerignore"
            exit 1
        fi
    fi
    
    log_success "安全配置验证通过"
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理旧的镜像..."
    
    # 删除悬空镜像
    local dangling_images=$(docker images -f "dangling=true" -q)
    if [ -n "$dangling_images" ]; then
        docker rmi $dangling_images || true
        log_success "清理了悬空镜像"
    fi
    
    # 删除旧版本镜像（保留最新的3个版本）
    local old_images=$(docker images "$IMAGE_NAME" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | tail -n +2 | sort -k2 -r | tail -n +4 | awk '{print $1}')
    if [ -n "$old_images" ]; then
        echo "$old_images" | xargs docker rmi || true
        log_success "清理了旧版本镜像"
    fi
}

# 构建镜像
build_image() {
    log_info "开始构建MySQL镜像..."
    log_info "镜像名称: $IMAGE_NAME:$IMAGE_TAG"
    log_info "构建上下文: $BUILD_CONTEXT"
    log_info "Dockerfile: $DOCKERFILE"
    
    # 构建参数
    local build_args=(
        "--file" "$DOCKERFILE"
        "--tag" "$IMAGE_NAME:$IMAGE_TAG"
        "--label" "com.dl-engine.service=mysql"
        "--label" "com.dl-engine.version=1.0.0"
        "--label" "com.dl-engine.build-date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')"
        "--label" "com.dl-engine.description=DL引擎MySQL数据库服务"
    )
    
    # 如果是生产构建，添加优化参数
    if [ "$BUILD_ENV" = "production" ]; then
        build_args+=(
            "--no-cache"
            "--pull"
        )
        log_info "生产环境构建，启用无缓存模式"
    fi
    
    # 执行构建
    if docker build "${build_args[@]}" "$BUILD_CONTEXT"; then
        log_success "镜像构建成功: $IMAGE_NAME:$IMAGE_TAG"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 验证镜像
validate_image() {
    log_info "验证构建的镜像..."
    
    # 检查镜像是否存在
    if ! docker images "$IMAGE_NAME:$IMAGE_TAG" | grep -q "$IMAGE_NAME"; then
        log_error "镜像不存在: $IMAGE_NAME:$IMAGE_TAG"
        exit 1
    fi
    
    # 检查镜像大小
    local image_size=$(docker images "$IMAGE_NAME:$IMAGE_TAG" --format "{{.Size}}")
    log_info "镜像大小: $image_size"
    
    # 检查镜像层数
    local layer_count=$(docker history "$IMAGE_NAME:$IMAGE_TAG" --quiet | wc -l)
    log_info "镜像层数: $layer_count"
    
    # 安全扫描（如果有工具）
    if command -v trivy &> /dev/null; then
        log_info "执行安全扫描..."
        trivy image "$IMAGE_NAME:$IMAGE_TAG" || log_warning "安全扫描发现问题，请检查"
    fi
    
    log_success "镜像验证完成"
}

# 测试镜像
test_image() {
    log_info "测试构建的镜像..."
    
    # 创建测试容器
    local test_container="test-$IMAGE_NAME-$(date +%s)"
    
    log_info "创建测试容器: $test_container"
    
    # 运行测试容器
    if docker run -d \
        --name "$test_container" \
        -e MYSQL_ROOT_PASSWORD=test_password_123 \
        -e MYSQL_ALLOW_EMPTY_PASSWORD=no \
        "$IMAGE_NAME:$IMAGE_TAG"; then
        
        log_success "测试容器启动成功"
        
        # 等待MySQL启动
        log_info "等待MySQL服务启动..."
        sleep 30
        
        # 测试健康检查
        if docker exec "$test_container" /usr/local/bin/health-check.sh simple; then
            log_success "健康检查测试通过"
        else
            log_error "健康检查测试失败"
        fi
        
        # 清理测试容器
        docker stop "$test_container" >/dev/null 2>&1
        docker rm "$test_container" >/dev/null 2>&1
        log_success "测试容器已清理"
        
    else
        log_error "测试容器启动失败"
        exit 1
    fi
}

# 显示构建信息
show_build_info() {
    log_info "构建完成信息:"
    echo "========================================"
    echo "镜像名称: $IMAGE_NAME:$IMAGE_TAG"
    echo "构建时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "镜像ID: $(docker images "$IMAGE_NAME:$IMAGE_TAG" --format "{{.ID}}")"
    echo "镜像大小: $(docker images "$IMAGE_NAME:$IMAGE_TAG" --format "{{.Size}}")"
    echo ""
    echo "使用方法:"
    echo "  docker run -d \\"
    echo "    --name dl-engine-mysql \\"
    echo "    -e MYSQL_ROOT_PASSWORD=your_secure_password \\"
    echo "    -p 3306:3306 \\"
    echo "    $IMAGE_NAME:$IMAGE_TAG"
    echo ""
    echo "或使用docker-compose:"
    echo "  docker-compose up -d dl-engine-mysql"
    echo "========================================"
}

# 主构建函数
main() {
    log_info "开始DL引擎MySQL服务构建..."
    
    # 检查环境
    check_environment
    
    # 验证安全配置
    validate_security
    
    # 清理旧镜像
    if [ "$CLEANUP" = "true" ]; then
        cleanup_old_images
    fi
    
    # 构建镜像
    build_image
    
    # 验证镜像
    validate_image
    
    # 测试镜像
    if [ "$SKIP_TEST" != "true" ]; then
        test_image
    fi
    
    # 显示构建信息
    show_build_info
    
    log_success "MySQL服务构建完成"
}

# 显示帮助信息
show_help() {
    echo "DL引擎MySQL服务构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --image-name NAME       镜像名称 (默认: dl-engine-mysql)"
    echo "  --image-tag TAG         镜像标签 (默认: latest)"
    echo "  --build-env ENV         构建环境 (development/production)"
    echo "  --cleanup               清理旧镜像"
    echo "  --skip-test             跳过镜像测试"
    echo "  --help                  显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  IMAGE_NAME              镜像名称"
    echo "  IMAGE_TAG               镜像标签"
    echo "  BUILD_ENV               构建环境"
    echo "  CLEANUP                 是否清理旧镜像"
    echo "  SKIP_TEST               是否跳过测试"
    echo ""
    echo "示例:"
    echo "  $0                      # 默认构建"
    echo "  $0 --cleanup            # 构建并清理旧镜像"
    echo "  $0 --build-env production --image-tag v1.0.0"
}

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --image-name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --build-env)
            BUILD_ENV="$2"
            shift 2
            ;;
        --cleanup)
            CLEANUP="true"
            shift
            ;;
        --skip-test)
            SKIP_TEST="true"
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
