{"name": "monitoring-service", "version": "1.0.0", "description": "监控服务 - 负责系统监控、告警、日志分析和通知", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "docker:build": "docker build -t monitoring-service .", "docker:run": "docker run -p 3100:3100 monitoring-service", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:dev:logs": "docker-compose -f docker-compose.dev.yml logs -f", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "health:check": "chmod +x scripts/health-check.sh && ./scripts/health-check.sh", "db:migrate": "npm run build && node dist/database/migrate.js", "db:seed": "npm run build && node dist/database/seed.js", "clean": "rm -rf dist node_modules", "precommit": "npm run lint:check && npm run test"}, "dependencies": {"@elastic/elasticsearch": "^9.0.2", "@nestjs/axios": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/terminus": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@willsoto/nestjs-prometheus": "^6.0.0", "axios": "^1.5.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cron": "^3.1.3", "mysql2": "^3.6.0", "nodemailer": "^6.9.4", "prom-client": "^15.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.9", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}