@echo off
echo ================================
echo 检查Service Registry导入路径
echo ================================

echo.
echo 检查是否还有旧的导入路径...

echo.
echo 检查 ../../../../shared 导入:
findstr /s /n "../../../../shared" src\*.ts 2>nul
if %errorlevel% equ 0 (
    echo 发现旧的导入路径！
) else (
    echo ✓ 未发现 ../../../../shared 导入
)

echo.
echo 检查 ../../../shared 导入:
findstr /s /n "../../../shared" src\*.ts 2>nul
if %errorlevel% equ 0 (
    echo 发现旧的导入路径！
) else (
    echo ✓ 未发现 ../../../shared 导入
)

echo.
echo 检查本地shared导入:
findstr /s /n "shared/event-bus" src\*.ts 2>nul
if %errorlevel% equ 0 (
    echo ✓ 发现本地shared导入
) else (
    echo ❌ 未发现本地shared导入
)

echo.
echo 检查完成！
pause
