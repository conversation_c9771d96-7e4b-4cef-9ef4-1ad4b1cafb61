#!/bin/bash

# DL引擎Redis启动脚本
# 负责启动Redis服务并进行初始化配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 配置变量
REDIS_CONF=${REDIS_CONF:-"/usr/local/etc/redis/redis.conf"}
REDIS_DATA_DIR=${REDIS_DATA_DIR:-"/var/lib/redis"}
REDIS_LOG_DIR=${REDIS_LOG_DIR:-"/var/log/redis"}
REDIS_PASSWORD=${REDIS_PASSWORD:-""}
REDIS_MAX_MEMORY=${REDIS_MAX_MEMORY:-""}

# 检查配置文件
check_config() {
    log_info "检查Redis配置文件..."
    
    if [ ! -f "$REDIS_CONF" ]; then
        log_error "Redis配置文件不存在: $REDIS_CONF"
        exit 1
    fi
    
    log_success "Redis配置文件检查通过: $REDIS_CONF"
}

# 检查目录权限
check_directories() {
    log_info "检查目录权限..."
    
    # 检查数据目录
    if [ ! -d "$REDIS_DATA_DIR" ]; then
        log_warning "数据目录不存在，创建: $REDIS_DATA_DIR"
        mkdir -p "$REDIS_DATA_DIR"
    fi
    
    if [ ! -w "$REDIS_DATA_DIR" ]; then
        log_error "数据目录不可写: $REDIS_DATA_DIR"
        exit 1
    fi
    
    # 检查日志目录
    if [ ! -d "$REDIS_LOG_DIR" ]; then
        log_warning "日志目录不存在，创建: $REDIS_LOG_DIR"
        mkdir -p "$REDIS_LOG_DIR"
    fi
    
    if [ ! -w "$REDIS_LOG_DIR" ]; then
        log_error "日志目录不可写: $REDIS_LOG_DIR"
        exit 1
    fi
    
    log_success "目录权限检查通过"
}

# 动态配置Redis
configure_redis() {
    log_info "配置Redis动态参数..."
    
    local temp_conf="/tmp/redis_dynamic.conf"
    cp "$REDIS_CONF" "$temp_conf"
    
    # 设置密码
    if [ -n "$REDIS_PASSWORD" ]; then
        log_info "设置Redis密码认证"
        echo "requirepass $REDIS_PASSWORD" >> "$temp_conf"
    fi
    
    # 设置最大内存
    if [ -n "$REDIS_MAX_MEMORY" ]; then
        log_info "设置Redis最大内存: $REDIS_MAX_MEMORY"
        echo "maxmemory $REDIS_MAX_MEMORY" >> "$temp_conf"
    fi
    
    # 根据环境变量调整配置
    if [ "$REDIS_ENABLE_AOF" = "false" ]; then
        log_info "禁用AOF持久化"
        sed -i 's/appendonly yes/appendonly no/' "$temp_conf"
    fi
    
    if [ "$REDIS_ENABLE_RDB" = "false" ]; then
        log_info "禁用RDB持久化"
        sed -i '/^save /d' "$temp_conf"
    fi
    
    # 设置日志级别
    if [ -n "$REDIS_LOG_LEVEL" ]; then
        log_info "设置日志级别: $REDIS_LOG_LEVEL"
        sed -i "s/loglevel notice/loglevel $REDIS_LOG_LEVEL/" "$temp_conf"
    fi
    
    REDIS_CONF="$temp_conf"
    log_success "Redis动态配置完成"
}

# 初始化数据库
init_databases() {
    log_info "初始化Redis数据库分配..."
    
    # 创建数据库分配说明文件
    cat > "$REDIS_DATA_DIR/database_allocation.txt" << EOF
DL引擎Redis数据库分配说明
创建时间: $(date '+%Y-%m-%d %H:%M:%S')

数据库分配:
DB 0: 服务注册中心缓存
DB 1: 用户服务缓存和会话
DB 2: 项目服务缓存
DB 3: 资产服务缓存
DB 4: 渲染服务任务队列
DB 5: 协作服务实时数据
DB 6: 监控服务缓存
DB 7: API网关缓存和限流
DB 8: 事件总线消息
DB 9: 分布式锁
DB 10: 临时数据
DB 11-15: 预留扩展

键命名规范:
- 服务前缀: service_name:
- 缓存: cache:key_name
- 会话: session:session_id
- 队列: queue:queue_name
- 锁: lock:resource_name
- 事件: event:event_type
EOF
    
    log_success "数据库分配说明已创建"
}

# 预热缓存
warm_up_cache() {
    log_info "预热Redis缓存..."
    
    # 这里可以添加预热逻辑
    # 例如：预加载常用配置、初始化队列等
    
    log_success "缓存预热完成"
}

# 启动监控
start_monitoring() {
    log_info "启动Redis监控..."
    
    # 在后台启动监控脚本
    if [ -f "/usr/local/bin/monitor.sh" ]; then
        nohup /usr/local/bin/monitor.sh > "$REDIS_LOG_DIR/monitor.log" 2>&1 &
        log_success "Redis监控已启动"
    else
        log_warning "监控脚本不存在，跳过监控启动"
    fi
}

# 设置信号处理
setup_signal_handlers() {
    log_info "设置信号处理器..."
    
    # 优雅关闭处理
    trap 'log_info "收到SIGTERM信号，正在优雅关闭Redis..."; redis-cli SHUTDOWN SAVE; exit 0' TERM
    trap 'log_info "收到SIGINT信号，正在优雅关闭Redis..."; redis-cli SHUTDOWN SAVE; exit 0' INT
    
    log_success "信号处理器设置完成"
}

# 显示启动信息
show_startup_info() {
    log_info "DL引擎Redis服务启动信息:"
    echo "========================================"
    echo "Redis版本: $(redis-server --version)"
    echo "配置文件: $REDIS_CONF"
    echo "数据目录: $REDIS_DATA_DIR"
    echo "日志目录: $REDIS_LOG_DIR"
    echo "启动时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "进程ID: $$"
    echo "========================================"
}

# 主启动函数
main() {
    log_info "开始启动DL引擎Redis服务..."
    
    # 执行启动前检查
    check_config
    check_directories
    configure_redis
    init_databases
    
    # 设置信号处理
    setup_signal_handlers
    
    # 显示启动信息
    show_startup_info
    
    # 预热缓存
    warm_up_cache
    
    # 启动监控
    start_monitoring
    
    log_success "Redis服务启动准备完成"
    log_info "正在启动Redis服务器..."
    
    # 启动Redis服务器
    exec redis-server "$REDIS_CONF"
}

# 帮助信息
show_help() {
    echo "DL引擎Redis启动脚本"
    echo ""
    echo "环境变量:"
    echo "  REDIS_CONF              Redis配置文件路径"
    echo "  REDIS_DATA_DIR          Redis数据目录"
    echo "  REDIS_LOG_DIR           Redis日志目录"
    echo "  REDIS_PASSWORD          Redis密码"
    echo "  REDIS_MAX_MEMORY        Redis最大内存"
    echo "  REDIS_ENABLE_AOF        是否启用AOF (true/false)"
    echo "  REDIS_ENABLE_RDB        是否启用RDB (true/false)"
    echo "  REDIS_LOG_LEVEL         日志级别 (debug/verbose/notice/warning)"
    echo ""
    echo "用法:"
    echo "  $0                      启动Redis服务"
    echo "  $0 --help              显示帮助信息"
}

# 处理命令行参数
case "${1:-}" in
    "--help"|"-h")
        show_help
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
