-- 默认数据初始化脚本
-- 为各个微服务创建默认数据

-- 设置字符集
SET NAMES utf8mb4;

-- ================================
-- 用户服务默认数据
-- ================================
USE dl_engine_users;

-- 注意：这里只是预留表结构，实际的用户表会由TypeORM自动创建
-- 但我们可以在应用启动后插入默认管理员用户

-- ================================
-- 服务注册中心默认数据
-- ================================
USE dl_engine_registry;

-- 预留服务注册中心的默认配置数据

-- ================================
-- 监控服务默认数据
-- ================================
USE monitoring;

-- 插入默认告警规则
INSERT IGNORE INTO alert_rules (name, description, metric, operator, threshold, duration, severity, enabled, labels, annotations) VALUES
('CPU使用率过高', 'CPU使用率超过80%持续5分钟', 'cpu_usage_percent', '>', 80.0, 300, 'warning', true, 
 '{"component": "system", "type": "resource"}',
 '{"summary": "CPU使用率过高", "description": "系统CPU使用率超过80%，请检查系统负载"}'),

('内存使用率过高', '内存使用率超过85%持续5分钟', 'memory_usage_percent', '>', 85.0, 300, 'warning', true,
 '{"component": "system", "type": "resource"}',
 '{"summary": "内存使用率过高", "description": "系统内存使用率超过85%，请检查内存泄漏"}'),

('磁盘使用率过高', '磁盘使用率超过90%', 'disk_usage_percent', '>', 90.0, 60, 'error', true,
 '{"component": "system", "type": "resource"}',
 '{"summary": "磁盘使用率过高", "description": "磁盘使用率超过90%，请及时清理磁盘空间"}'),

('服务响应时间过长', '服务响应时间超过5秒', 'service_response_time', '>', 5000.0, 120, 'warning', true,
 '{"component": "service", "type": "performance"}',
 '{"summary": "服务响应时间过长", "description": "服务响应时间超过5秒，请检查服务性能"}'),

('服务错误率过高', '服务错误率超过5%', 'service_error_rate', '>', 0.05, 180, 'error', true,
 '{"component": "service", "type": "reliability"}',
 '{"summary": "服务错误率过高", "description": "服务错误率超过5%，请检查服务稳定性"}'),

('数据库连接数过多', '数据库连接数超过400', 'mysql_connections', '>', 400.0, 300, 'warning', true,
 '{"component": "database", "type": "resource"}',
 '{"summary": "数据库连接数过多", "description": "MySQL连接数超过400，请检查连接池配置"}'),

('Redis内存使用过高', 'Redis内存使用超过80%', 'redis_memory_usage_percent', '>', 80.0, 300, 'warning', true,
 '{"component": "cache", "type": "resource"}',
 '{"summary": "Redis内存使用过高", "description": "Redis内存使用率超过80%，请检查缓存策略"}');

-- 插入默认服务状态记录
INSERT IGNORE INTO service_status (serviceName, serviceUrl, status, metadata) VALUES
('user-service', 'http://user-service:4001', 'unknown', '{"description": "用户管理服务", "version": "1.0.0"}'),
('project-service', 'http://project-service:4002', 'unknown', '{"description": "项目管理服务", "version": "1.0.0"}'),
('asset-service', 'http://asset-service:4003', 'unknown', '{"description": "资产管理服务", "version": "1.0.0"}'),
('render-service', 'http://render-service:4004', 'unknown', '{"description": "渲染处理服务", "version": "1.0.0"}'),
('collaboration-service', 'http://collaboration-service:3005', 'unknown', '{"description": "协作服务", "version": "1.0.0"}'),
('api-gateway', 'http://api-gateway:3000', 'unknown', '{"description": "API网关服务", "version": "1.0.0"}'),
('service-registry', 'http://service-registry:4010', 'unknown', '{"description": "服务注册中心", "version": "1.0.0"}'),
('game-server', 'http://game-server:3030', 'unknown', '{"description": "游戏服务器", "version": "1.0.0"}'),
('monitoring-service', 'http://monitoring-service:3100', 'unknown', '{"description": "监控服务", "version": "1.0.0"}');

-- 插入系统信息记录
INSERT IGNORE INTO log_entries (level, message, service, module, context, timestamp) VALUES
('info', 'DL引擎MySQL数据库初始化完成', 'mysql-service', 'init', 
 '{"databases": ["dl_engine_registry", "dl_engine_users", "dl_engine_projects", "dl_engine_assets", "dl_engine_render", "monitoring"], "version": "1.0.0"}',
 NOW());

-- ================================
-- 创建数据库视图（便于监控）
-- ================================

-- 服务健康状态视图
CREATE OR REPLACE VIEW v_service_health AS
SELECT 
    serviceName,
    status,
    responseTime,
    lastCheckAt,
    lastHealthyAt,
    consecutiveFailures,
    CASE 
        WHEN status = 'healthy' THEN '正常'
        WHEN status = 'degraded' THEN '降级'
        WHEN status = 'unhealthy' THEN '异常'
        ELSE '未知'
    END AS status_desc,
    CASE 
        WHEN lastCheckAt IS NULL THEN '从未检查'
        WHEN lastCheckAt < DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN '检查超时'
        ELSE '检查正常'
    END AS check_status
FROM service_status
ORDER BY 
    CASE status 
        WHEN 'unhealthy' THEN 1
        WHEN 'degraded' THEN 2
        WHEN 'unknown' THEN 3
        WHEN 'healthy' THEN 4
    END,
    serviceName;

-- 活跃告警视图
CREATE OR REPLACE VIEW v_active_alerts AS
SELECT 
    a.id,
    ar.name as rule_name,
    ar.severity,
    a.status,
    a.startsAt,
    a.endsAt,
    TIMESTAMPDIFF(MINUTE, a.startsAt, COALESCE(a.endsAt, NOW())) as duration_minutes,
    ar.description,
    a.labels,
    a.annotations
FROM alerts a
JOIN alert_rules ar ON a.ruleId = ar.id
WHERE a.status = 'firing'
ORDER BY 
    CASE ar.severity 
        WHEN 'critical' THEN 1
        WHEN 'error' THEN 2
        WHEN 'warning' THEN 3
        WHEN 'info' THEN 4
    END,
    a.startsAt DESC;

-- 系统指标统计视图
CREATE OR REPLACE VIEW v_metrics_summary AS
SELECT 
    service,
    name as metric_name,
    COUNT(*) as record_count,
    AVG(value) as avg_value,
    MIN(value) as min_value,
    MAX(value) as max_value,
    MAX(timestamp) as last_update
FROM metrics 
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY service, name
ORDER BY service, name;

-- ================================
-- 创建存储过程（数据清理）
-- ================================

DELIMITER //

-- 清理过期数据的存储过程
CREATE PROCEDURE CleanupOldData()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE cleanup_date DATE;
    
    -- 设置清理日期（保留30天数据）
    SET cleanup_date = DATE_SUB(CURDATE(), INTERVAL 30 DAY);
    
    -- 清理过期指标数据
    DELETE FROM metrics WHERE DATE(timestamp) < cleanup_date;
    
    -- 清理过期日志数据
    DELETE FROM log_entries WHERE DATE(timestamp) < cleanup_date;
    
    -- 清理过期健康检查数据
    DELETE FROM health_checks WHERE DATE(createdAt) < cleanup_date;
    
    -- 清理已发送的通知记录（保留7天）
    DELETE FROM notifications 
    WHERE status = 'sent' 
    AND DATE(sentAt) < DATE_SUB(CURDATE(), INTERVAL 7 DAY);
    
    -- 记录清理日志
    INSERT INTO log_entries (level, message, service, module, context, timestamp) 
    VALUES ('info', '数据清理完成', 'mysql-service', 'cleanup', 
            JSON_OBJECT('cleanup_date', cleanup_date, 'timestamp', NOW()), NOW());
            
END //

DELIMITER ;

-- ================================
-- 输出初始化完成信息
-- ================================

SELECT 
    'DL引擎数据库初始化完成' AS status,
    NOW() AS timestamp,
    (SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name LIKE '%engine%' OR schema_name = 'monitoring') AS database_count,
    (SELECT COUNT(*) FROM alert_rules) AS default_alert_rules,
    (SELECT COUNT(*) FROM service_status) AS default_services,
    @@version AS mysql_version;
