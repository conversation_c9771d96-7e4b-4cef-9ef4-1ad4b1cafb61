# DL引擎MySQL服务快速启动指南

## 🚀 快速开始

### 1. 准备环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，设置安全密码
# 重要：请将 MYSQL_ROOT_PASSWORD 设置为强密码
vim .env
```

**必需配置：**
```bash
# 设置强密码（至少12位，包含大小写字母、数字和特殊字符）
MYSQL_ROOT_PASSWORD=YourSecurePassword123!@#
```

### 2. 构建镜像

#### 方法一：使用构建脚本（推荐）
```bash
# Linux/Mac
./build.sh

# Windows
build.bat
```

#### 方法二：直接使用Docker
```bash
docker build -t dl-engine-mysql .
```

### 3. 启动服务

#### 方法一：使用Docker Compose（推荐）
```bash
# 启动MySQL服务
docker-compose up -d dl-engine-mysql

# 查看日志
docker-compose logs -f dl-engine-mysql
```

#### 方法二：直接使用Docker
```bash
docker run -d \
  --name dl-engine-mysql \
  -e MYSQL_ROOT_PASSWORD=YourSecurePassword123!@# \
  -p 3306:3306 \
  -v mysql_data:/var/lib/mysql \
  dl-engine-mysql
```

### 4. 验证安装

```bash
# 健康检查
docker exec dl-engine-mysql /usr/local/bin/health-check.sh

# 连接测试
mysql -h localhost -P 3306 -u root -p

# 查看数据库
mysql -h localhost -P 3306 -u root -p -e "SHOW DATABASES;"
```

## 🔧 常用操作

### 数据库管理

```bash
# 查看所有数据库
docker exec dl-engine-mysql mysql -u root -p -e "SHOW DATABASES;"

# 连接到特定数据库
docker exec -it dl-engine-mysql mysql -u root -p dl_engine_users

# 查看用户权限
docker exec dl-engine-mysql mysql -u root -p -e "SELECT User, Host FROM mysql.user;"
```

### 备份和恢复

```bash
# 完整备份
docker exec dl-engine-mysql /usr/local/bin/backup.sh full

# 备份特定数据库
docker exec dl-engine-mysql /usr/local/bin/backup.sh database dl_engine_users

# 查看备份状态
docker exec dl-engine-mysql /usr/local/bin/backup.sh status

# 恢复最新备份
docker exec dl-engine-mysql /usr/local/bin/restore.sh latest dl_engine_users
```

### 监控和日志

```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f dl-engine-mysql

# 查看MySQL错误日志
docker exec dl-engine-mysql tail -f /var/log/mysql/error.log

# 性能监控
docker exec dl-engine-mysql /usr/local/bin/monitor.sh once
```

## 🌐 Web管理界面

### phpMyAdmin（可选）

```bash
# 启动phpMyAdmin
docker-compose up -d phpmyadmin

# 访问地址
http://localhost:8080

# 登录信息
用户名: root
密码: 您设置的MYSQL_ROOT_PASSWORD
```

## 🔗 微服务连接

### 连接配置

各微服务可以使用以下配置连接MySQL：

```javascript
// Node.js 示例
const mysql = require('mysql2');

const connection = mysql.createConnection({
  host: 'dl-engine-mysql',  // Docker网络中的服务名
  port: 3306,
  user: 'root',
  password: process.env.MYSQL_ROOT_PASSWORD,
  database: 'dl_engine_users'  // 根据服务选择对应数据库
});
```

### 数据库分配

| 服务 | 数据库名称 | 用途 |
|------|-----------|------|
| 服务注册中心 | `dl_engine_registry` | 服务实例、健康检查 |
| 用户服务 | `dl_engine_users` | 用户信息、认证、权限 |
| 项目服务 | `dl_engine_projects` | 项目、场景、版本管理 |
| 资产服务 | `dl_engine_assets` | 3D模型、纹理、音频 |
| 渲染服务 | `dl_engine_render` | 渲染任务、队列、历史 |
| 监控服务 | `monitoring` | 指标、告警、日志 |

## 🛠️ 故障排除

### 常见问题

1. **构建时出现安全警告**
   ```bash
   # 确保没有在Dockerfile中硬编码密码
   # 密码应通过环境变量在运行时传入
   ```

2. **连接失败**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 检查网络
   docker network ls
   
   # 检查端口
   netstat -tulpn | grep 3306
   ```

3. **密码问题**
   ```bash
   # 重置密码
   docker exec -it dl-engine-mysql mysql -u root -p
   ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';
   FLUSH PRIVILEGES;
   ```

4. **数据丢失**
   ```bash
   # 检查数据卷
   docker volume ls
   
   # 从备份恢复
   docker exec dl-engine-mysql /usr/local/bin/restore.sh latest
   ```

### 日志查看

```bash
# MySQL错误日志
docker exec dl-engine-mysql tail -f /var/log/mysql/error.log

# MySQL慢查询日志
docker exec dl-engine-mysql tail -f /var/log/mysql/slow.log

# 容器日志
docker logs dl-engine-mysql

# 健康检查日志
docker exec dl-engine-mysql /usr/local/bin/health-check.sh full
```

## 📋 检查清单

启动前请确认：

- [ ] 已设置强密码（MYSQL_ROOT_PASSWORD）
- [ ] 已复制并编辑.env文件
- [ ] Docker和Docker Compose已安装
- [ ] 端口3306未被占用
- [ ] 有足够的磁盘空间（建议至少5GB）

启动后请验证：

- [ ] 容器状态为healthy
- [ ] 可以连接到MySQL
- [ ] 所有数据库已创建
- [ ] 健康检查通过
- [ ] 备份功能正常

## 🔒 安全建议

1. **密码安全**
   - 使用至少12位的复杂密码
   - 定期更换密码
   - 不要在代码中硬编码密码

2. **网络安全**
   - 限制MySQL端口访问
   - 使用Docker网络隔离
   - 启用SSL连接（生产环境）

3. **数据安全**
   - 定期备份数据
   - 验证备份完整性
   - 加密敏感数据

4. **访问控制**
   - 为每个微服务创建专用用户
   - 限制用户权限
   - 定期审计用户权限

更多详细信息请参考 [SECURITY.md](SECURITY.md) 和 [README.md](README.md)。
