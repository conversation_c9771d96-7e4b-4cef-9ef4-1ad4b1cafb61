version: '3.8'

services:
  # DL引擎Redis缓存服务
  dl-engine-redis:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dl-engine-redis
    restart: unless-stopped
    
    # 环境变量
    environment:
      # Redis基本配置
      REDIS_PASSWORD: ${REDIS_PASSWORD:-""}
      REDIS_MAX_MEMORY: ${REDIS_MAX_MEMORY:-"1gb"}
      
      # 持久化配置
      REDIS_ENABLE_AOF: ${REDIS_ENABLE_AOF:-"true"}
      REDIS_ENABLE_RDB: ${REDIS_ENABLE_RDB:-"true"}
      
      # 日志配置
      REDIS_LOG_LEVEL: ${REDIS_LOG_LEVEL:-"notice"}
      
      # 监控配置
      MONITOR_INTERVAL: ${MONITOR_INTERVAL:-"30"}
      
      # 备份配置
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-"7"}
      
      # 时区配置
      TZ: Asia/Shanghai
    
    # 端口映射
    ports:
      - "${REDIS_PORT:-6379}:6379"
    
    # 数据卷
    volumes:
      # 数据持久化
      - redis_data:/var/lib/redis
      
      # 日志目录
      - redis_logs:/var/log/redis
      
      # 备份目录
      - redis_backups:/var/lib/redis/backups
      
      # 配置文件（可选，用于外部配置覆盖）
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
      
      # 自定义脚本（可选）
      - ./custom-scripts:/usr/local/bin/custom:ro
    
    # 网络配置
    networks:
      - dl-engine-network
    
    # 健康检查
    healthcheck:
      test: ["/usr/local/bin/health-check.sh", "simple"]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT:-1G}
          cpus: ${REDIS_CPU_LIMIT:-"0.5"}
        reservations:
          memory: ${REDIS_MEMORY_RESERVATION:-512M}
          cpus: ${REDIS_CPU_RESERVATION:-"0.25"}
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    
    # 标签
    labels:
      - "com.dl-engine.service=redis"
      - "com.dl-engine.version=1.0.0"
      - "com.dl-engine.description=DL引擎Redis缓存服务"

  # Redis管理界面 (可选)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: dl-engine-redis-commander
    restart: unless-stopped
    
    environment:
      REDIS_HOSTS: "dl-engine:dl-engine-redis:6379"
      REDIS_PASSWORD: ${REDIS_PASSWORD:-""}
      HTTP_USER: ${REDIS_COMMANDER_USER:-"admin"}
      HTTP_PASSWORD: ${REDIS_COMMANDER_PASSWORD:-"admin"}
      
    ports:
      - "${REDIS_COMMANDER_PORT:-8081}:8081"
    
    depends_on:
      dl-engine-redis:
        condition: service_healthy
    
    networks:
      - dl-engine-network
    
    labels:
      - "com.dl-engine.service=redis-commander"
      - "com.dl-engine.description=Redis管理界面"

  # Redis监控服务
  redis-monitor:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dl-engine-redis-monitor
    restart: unless-stopped
    
    environment:
      REDIS_HOST: dl-engine-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-""}
      MONITOR_INTERVAL: ${MONITOR_INTERVAL:-"30"}
      MONITOR_LOG_FILE: /var/log/redis/monitor.log
      
    volumes:
      - redis_logs:/var/log/redis
      
    networks:
      - dl-engine-network
    
    depends_on:
      dl-engine-redis:
        condition: service_healthy
    
    # 运行监控脚本
    command: ["/usr/local/bin/monitor.sh", "monitor"]
    
    labels:
      - "com.dl-engine.service=redis-monitor"
      - "com.dl-engine.description=Redis监控服务"

  # Redis备份服务 (定时备份)
  redis-backup:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dl-engine-redis-backup
    restart: unless-stopped
    
    environment:
      REDIS_HOST: dl-engine-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-""}
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-"7"}
      BACKUP_DIR: /var/lib/redis/backups
      
    volumes:
      - redis_data:/var/lib/redis:ro
      - redis_backups:/var/lib/redis/backups
      - redis_logs:/var/log/redis
      
    networks:
      - dl-engine-network
    
    depends_on:
      dl-engine-redis:
        condition: service_healthy
    
    # 定时备份任务
    command: >
      sh -c "
        # 安装cron
        apk add --no-cache dcron &&
        
        # 创建备份任务
        echo '0 3 * * * /usr/local/bin/backup.sh full >> /var/log/redis/backup.log 2>&1' | crontab - &&
        echo '0 4 * * 0 /usr/local/bin/backup.sh cleanup >> /var/log/redis/backup.log 2>&1' | crontab - &&
        
        # 启动cron服务
        crond -f -d 8
      "
    
    labels:
      - "com.dl-engine.service=redis-backup"
      - "com.dl-engine.description=Redis定时备份服务"

  # Redis Exporter (Prometheus监控)
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: dl-engine-redis-exporter
    restart: unless-stopped
    
    environment:
      REDIS_ADDR: "redis://dl-engine-redis:6379"
      REDIS_PASSWORD: ${REDIS_PASSWORD:-""}
      
    ports:
      - "${REDIS_EXPORTER_PORT:-9121}:9121"
    
    depends_on:
      dl-engine-redis:
        condition: service_healthy
    
    networks:
      - dl-engine-network
    
    labels:
      - "com.dl-engine.service=redis-exporter"
      - "com.dl-engine.description=Redis Prometheus监控导出器"

# 数据卷定义
volumes:
  # Redis数据卷
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${REDIS_DATA_DIR:-./data/redis}
  
  # Redis日志卷
  redis_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${REDIS_LOG_DIR:-./data/logs}
  
  # Redis备份卷
  redis_backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${REDIS_BACKUP_DIR:-./data/backups}

# 网络定义
networks:
  dl-engine-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
