#!/bin/bash

# DL引擎MySQL健康检查脚本
# 用于Docker健康检查和监控

set -e

# 配置变量
MYSQL_HOST=${MYSQL_HOST:-"localhost"}
MYSQL_PORT=${MYSQL_PORT:-"3306"}
MYSQL_USER=${MYSQL_USER:-"root"}
MYSQL_PASSWORD=${MYSQL_ROOT_PASSWORD:-"dl_engine_password_2024"}
TIMEOUT=${HEALTH_CHECK_TIMEOUT:-"10"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 检查MySQL服务是否运行
check_mysql_process() {
    log_info "检查MySQL进程..."
    
    if pgrep mysqld > /dev/null; then
        log_success "MySQL进程运行正常"
        return 0
    else
        log_error "MySQL进程未运行"
        return 1
    fi
}

# 检查MySQL端口是否监听
check_mysql_port() {
    log_info "检查MySQL端口 $MYSQL_PORT..."
    
    if netstat -ln | grep ":$MYSQL_PORT " > /dev/null 2>&1; then
        log_success "MySQL端口 $MYSQL_PORT 监听正常"
        return 0
    else
        log_error "MySQL端口 $MYSQL_PORT 未监听"
        return 1
    fi
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    
    if mysqladmin ping -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" --connect-timeout="$TIMEOUT" --silent 2>/dev/null; then
        log_success "MySQL连接正常"
        return 0
    else
        log_error "MySQL连接失败"
        return 1
    fi
}

# 检查数据库是否存在
check_databases() {
    log_info "检查DL引擎数据库..."
    
    local databases=("dl_engine_registry" "dl_engine_users" "dl_engine_projects" "dl_engine_assets" "dl_engine_render" "monitoring")
    local missing_dbs=()
    
    for db in "${databases[@]}"; do
        if mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $db;" 2>/dev/null; then
            log_success "数据库 $db 存在"
        else
            log_warning "数据库 $db 不存在"
            missing_dbs+=("$db")
        fi
    done
    
    if [ ${#missing_dbs[@]} -eq 0 ]; then
        log_success "所有DL引擎数据库检查通过"
        return 0
    else
        log_error "缺少数据库: ${missing_dbs[*]}"
        return 1
    fi
}

# 检查数据库性能
check_mysql_performance() {
    log_info "检查MySQL性能指标..."
    
    local result=$(mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "
        SELECT 
            VARIABLE_NAME, 
            VARIABLE_VALUE 
        FROM performance_schema.global_status 
        WHERE VARIABLE_NAME IN (
            'Threads_connected', 
            'Threads_running', 
            'Queries', 
            'Uptime',
            'Innodb_buffer_pool_read_requests',
            'Innodb_buffer_pool_reads'
        );" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        log_success "MySQL性能指标获取成功"
        
        # 解析连接数
        local connections=$(echo "$result" | grep "Threads_connected" | awk '{print $2}')
        local running=$(echo "$result" | grep "Threads_running" | awk '{print $2}')
        local uptime=$(echo "$result" | grep "Uptime" | awk '{print $2}')
        
        if [ -n "$connections" ]; then
            log_info "当前连接数: $connections"
            if [ "$connections" -gt 400 ]; then
                log_warning "连接数较高: $connections"
            fi
        fi
        
        if [ -n "$running" ]; then
            log_info "运行中线程: $running"
        fi
        
        if [ -n "$uptime" ]; then
            local uptime_hours=$((uptime / 3600))
            log_info "运行时间: ${uptime_hours} 小时"
        fi
        
        return 0
    else
        log_error "MySQL性能指标获取失败"
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    local data_dir="/var/lib/mysql"
    local usage=$(df "$data_dir" | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ -n "$usage" ]; then
        log_info "MySQL数据目录磁盘使用率: ${usage}%"
        
        if [ "$usage" -gt 90 ]; then
            log_error "磁盘空间不足: ${usage}%"
            return 1
        elif [ "$usage" -gt 80 ]; then
            log_warning "磁盘空间较少: ${usage}%"
        else
            log_success "磁盘空间充足: ${usage}%"
        fi
        return 0
    else
        log_error "无法获取磁盘使用率"
        return 1
    fi
}

# 检查错误日志
check_error_log() {
    log_info "检查MySQL错误日志..."
    
    local error_log="/var/log/mysql/error.log"
    
    if [ -f "$error_log" ]; then
        # 检查最近5分钟的错误
        local recent_errors=$(find "$error_log" -mmin -5 -exec grep -i "error\|warning" {} \; 2>/dev/null | wc -l)
        
        if [ "$recent_errors" -eq 0 ]; then
            log_success "最近5分钟无错误日志"
            return 0
        else
            log_warning "最近5分钟有 $recent_errors 条错误/警告日志"
            return 1
        fi
    else
        log_warning "错误日志文件不存在: $error_log"
        return 0
    fi
}

# 主健康检查函数
main_health_check() {
    local failed_checks=0
    local total_checks=0
    
    log_info "开始DL引擎MySQL健康检查..."
    echo "========================================"
    
    # 执行各项检查
    ((total_checks++))
    check_mysql_process || ((failed_checks++))
    
    ((total_checks++))
    check_mysql_port || ((failed_checks++))
    
    ((total_checks++))
    check_mysql_connection || ((failed_checks++))
    
    ((total_checks++))
    check_databases || ((failed_checks++))
    
    ((total_checks++))
    check_mysql_performance || ((failed_checks++))
    
    ((total_checks++))
    check_disk_space || ((failed_checks++))
    
    ((total_checks++))
    check_error_log || ((failed_checks++))
    
    echo "========================================"
    
    # 输出结果
    if [ $failed_checks -eq 0 ]; then
        log_success "所有健康检查通过 ($total_checks/$total_checks) ✓"
        exit 0
    else
        log_error "$failed_checks/$total_checks 项健康检查失败 ✗"
        exit 1
    fi
}

# 简单健康检查（用于Docker HEALTHCHECK）
simple_health_check() {
    # 只检查最基本的连接
    if mysqladmin ping -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" --connect-timeout="$TIMEOUT" --silent 2>/dev/null; then
        exit 0
    else
        exit 1
    fi
}

# 根据参数决定执行哪种检查
case "${1:-}" in
    "simple")
        simple_health_check
        ;;
    "full"|"")
        main_health_check
        ;;
    *)
        echo "用法: $0 [simple|full]"
        echo "  simple: 简单健康检查（仅连接测试）"
        echo "  full:   完整健康检查（默认）"
        exit 1
        ;;
esac
