#!/bin/bash

# 监控服务健康检查脚本

set -e

# 配置
HOST=${HEALTH_CHECK_HOST:-"localhost"}
PORT=${HEALTH_CHECK_PORT:-"3100"}
TIMEOUT=${HEALTH_CHECK_TIMEOUT:-"10"}
MAX_RETRIES=${HEALTH_CHECK_MAX_RETRIES:-"3"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service_running() {
    log_info "检查监控服务是否运行在 $HOST:$PORT..."
    
    if command -v nc &> /dev/null; then
        if nc -z "$HOST" "$PORT" 2>/dev/null; then
            log_success "服务端口 $PORT 可访问"
            return 0
        else
            log_error "服务端口 $PORT 不可访问"
            return 1
        fi
    else
        log_warning "nc命令不可用，跳过端口检查"
        return 0
    fi
}

# 检查健康端点
check_health_endpoint() {
    local url="http://$HOST:$PORT/api/v1/health"
    local retry=0
    
    log_info "检查健康端点: $url"
    
    while [ $retry -lt $MAX_RETRIES ]; do
        if curl -f -s --max-time "$TIMEOUT" "$url" > /dev/null; then
            log_success "健康检查端点响应正常"
            return 0
        else
            retry=$((retry + 1))
            if [ $retry -lt $MAX_RETRIES ]; then
                log_warning "健康检查失败，重试 $retry/$MAX_RETRIES..."
                sleep 2
            fi
        fi
    done
    
    log_error "健康检查端点无响应"
    return 1
}

# 检查Prometheus指标端点
check_metrics_endpoint() {
    local url="http://$HOST:$PORT/metrics"
    
    log_info "检查Prometheus指标端点: $url"
    
    if curl -f -s --max-time "$TIMEOUT" "$url" | head -n 1 | grep -q "^#"; then
        log_success "Prometheus指标端点响应正常"
        return 0
    else
        log_error "Prometheus指标端点无响应"
        return 1
    fi
}

# 检查API端点
check_api_endpoints() {
    local base_url="http://$HOST:$PORT/api/v1"
    local endpoints=("monitoring/services" "alerts" "logs" "notifications")
    local failed=0
    
    log_info "检查API端点..."
    
    for endpoint in "${endpoints[@]}"; do
        local url="$base_url/$endpoint"
        if curl -f -s --max-time "$TIMEOUT" "$url" > /dev/null; then
            log_success "API端点 /$endpoint 响应正常"
        else
            log_warning "API端点 /$endpoint 无响应"
            failed=$((failed + 1))
        fi
    done
    
    if [ $failed -eq 0 ]; then
        log_success "所有API端点检查通过"
        return 0
    else
        log_warning "$failed 个API端点检查失败"
        return 1
    fi
}

# 检查数据库连接
check_database_connection() {
    local url="http://$HOST:$PORT/api/v1/health"
    
    log_info "检查数据库连接状态..."
    
    local response=$(curl -f -s --max-time "$TIMEOUT" "$url" 2>/dev/null)
    if [ $? -eq 0 ]; then
        if echo "$response" | grep -q '"database".*"up"'; then
            log_success "数据库连接正常"
            return 0
        else
            log_error "数据库连接异常"
            return 1
        fi
    else
        log_error "无法获取数据库连接状态"
        return 1
    fi
}

# 检查内存使用情况
check_memory_usage() {
    log_info "检查内存使用情况..."
    
    if command -v ps &> /dev/null; then
        local memory_usage=$(ps aux | grep "node.*monitoring" | grep -v grep | awk '{sum += $6} END {print sum/1024}')
        if [ -n "$memory_usage" ]; then
            log_info "监控服务内存使用: ${memory_usage}MB"
            
            # 如果内存使用超过1GB，发出警告
            if (( $(echo "$memory_usage > 1024" | bc -l) )); then
                log_warning "内存使用较高: ${memory_usage}MB"
            fi
        else
            log_warning "无法获取内存使用情况"
        fi
    else
        log_warning "ps命令不可用，跳过内存检查"
    fi
}

# 生成健康报告
generate_health_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="health-report-$(date '+%Y%m%d-%H%M%S').json"
    
    log_info "生成健康报告: $report_file"
    
    cat > "$report_file" << EOF
{
  "timestamp": "$timestamp",
  "service": "monitoring-service",
  "host": "$HOST",
  "port": $PORT,
  "checks": {
    "service_running": $(check_service_running && echo "true" || echo "false"),
    "health_endpoint": $(check_health_endpoint && echo "true" || echo "false"),
    "metrics_endpoint": $(check_metrics_endpoint && echo "true" || echo "false"),
    "database_connection": $(check_database_connection && echo "true" || echo "false")
  }
}
EOF
    
    log_success "健康报告已生成: $report_file"
}

# 主健康检查函数
main_health_check() {
    local failed_checks=0
    
    log_info "开始监控服务健康检查..."
    log_info "目标服务: $HOST:$PORT"
    log_info "超时时间: ${TIMEOUT}秒"
    log_info "最大重试: $MAX_RETRIES 次"
    echo ""
    
    # 执行各项检查
    check_service_running || failed_checks=$((failed_checks + 1))
    check_health_endpoint || failed_checks=$((failed_checks + 1))
    check_metrics_endpoint || failed_checks=$((failed_checks + 1))
    check_api_endpoints || failed_checks=$((failed_checks + 1))
    check_database_connection || failed_checks=$((failed_checks + 1))
    check_memory_usage
    
    echo ""
    
    # 输出结果
    if [ $failed_checks -eq 0 ]; then
        log_success "所有健康检查通过 ✓"
        exit 0
    else
        log_error "$failed_checks 项健康检查失败 ✗"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "监控服务健康检查脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -r, --report        生成健康报告"
    echo "  --host HOST         指定主机地址 (默认: localhost)"
    echo "  --port PORT         指定端口 (默认: 3100)"
    echo "  --timeout SECONDS   指定超时时间 (默认: 10)"
    echo "  --retries COUNT     指定最大重试次数 (默认: 3)"
    echo ""
    echo "环境变量:"
    echo "  HEALTH_CHECK_HOST           主机地址"
    echo "  HEALTH_CHECK_PORT           端口"
    echo "  HEALTH_CHECK_TIMEOUT        超时时间"
    echo "  HEALTH_CHECK_MAX_RETRIES    最大重试次数"
    echo ""
    echo "示例:"
    echo "  $0                          # 执行健康检查"
    echo "  $0 --host *************     # 检查远程服务"
    echo "  $0 --port 3200              # 检查不同端口"
    echo "  $0 --report                 # 生成健康报告"
}

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -r|--report)
            generate_health_report
            exit 0
            ;;
        --host)
            HOST="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --retries)
            MAX_RETRIES="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主健康检查
main_health_check
