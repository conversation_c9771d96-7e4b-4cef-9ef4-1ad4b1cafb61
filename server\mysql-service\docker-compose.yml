version: '3.8'

services:
  # DL引擎MySQL数据库服务
  dl-engine-mysql:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dl-engine-mysql
    restart: unless-stopped
    
    # 环境变量
    environment:
      # MySQL基本配置
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-dl_engine_password_2024}
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
      MYSQL_RANDOM_ROOT_PASSWORD: 'no'
      
      # 字符集配置
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
      
      # 时区配置
      TZ: Asia/Shanghai
      
      # 备份配置
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-7}
      
    # 端口映射
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    
    # 数据卷
    volumes:
      # 数据持久化
      - mysql_data:/var/lib/mysql
      
      # 备份目录
      - mysql_backups:/var/lib/mysql-files/backups
      
      # 日志目录
      - mysql_logs:/var/log/mysql
      
      # 配置文件（可选，用于外部配置覆盖）
      - ./config/my.cnf:/etc/mysql/conf.d/dl-engine.cnf:ro
      
      # 自定义初始化脚本（可选）
      - ./custom-init:/docker-entrypoint-initdb.d/custom:ro
    
    # 网络配置
    networks:
      - dl-engine-network
    
    # 健康检查
    healthcheck:
      test: ["/usr/local/bin/health-check.sh", "simple"]
      interval: 30s
      timeout: 10s
      start_period: 60s
      retries: 3
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    
    # 用户配置
    user: mysql
    
    # 标签
    labels:
      - "com.dl-engine.service=mysql"
      - "com.dl-engine.version=1.0.0"
      - "com.dl-engine.description=DL引擎MySQL数据库服务"

  # MySQL管理工具 (可选)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: dl-engine-phpmyadmin
    restart: unless-stopped
    
    environment:
      PMA_HOST: dl-engine-mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: ${MYSQL_ROOT_PASSWORD:-dl_engine_password_2024}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-dl_engine_password_2024}
      
    ports:
      - "${PHPMYADMIN_PORT:-8080}:80"
    
    depends_on:
      dl-engine-mysql:
        condition: service_healthy
    
    networks:
      - dl-engine-network
    
    labels:
      - "com.dl-engine.service=phpmyadmin"
      - "com.dl-engine.description=MySQL管理界面"

  # MySQL备份服务 (定时备份)
  mysql-backup:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dl-engine-mysql-backup
    restart: unless-stopped
    
    environment:
      MYSQL_HOST: dl-engine-mysql
      MYSQL_PORT: 3306
      MYSQL_USER: root
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-dl_engine_password_2024}
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-7}
      
    volumes:
      - mysql_backups:/var/lib/mysql-files/backups
      
    networks:
      - dl-engine-network
    
    depends_on:
      dl-engine-mysql:
        condition: service_healthy
    
    # 定时备份任务
    command: >
      sh -c "
        # 安装cron
        apt-get update && apt-get install -y cron &&
        
        # 创建备份任务
        echo '0 2 * * * /usr/local/bin/backup.sh full >> /var/log/backup.log 2>&1' | crontab - &&
        echo '0 3 * * 0 /usr/local/bin/backup.sh cleanup >> /var/log/backup.log 2>&1' | crontab - &&
        
        # 启动cron服务
        cron &&
        
        # 保持容器运行
        tail -f /var/log/backup.log
      "
    
    labels:
      - "com.dl-engine.service=mysql-backup"
      - "com.dl-engine.description=MySQL定时备份服务"

# 数据卷定义
volumes:
  # MySQL数据卷
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${MYSQL_DATA_DIR:-./data/mysql}
  
  # MySQL备份卷
  mysql_backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${MYSQL_BACKUP_DIR:-./data/backups}
  
  # MySQL日志卷
  mysql_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${MYSQL_LOG_DIR:-./data/logs}

# 网络定义
networks:
  dl-engine-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
