import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index } from 'typeorm';

@Entity('metrics')
@Index(['name', 'timestamp'])
@Index(['service', 'timestamp'])
export class Metric {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column({ type: 'decimal', precision: 10, scale: 4 })
  value: number;

  @Column({ length: 50, nullable: true })
  service: string;

  @Column({ length: 50, nullable: true })
  instance: string;

  @Column({ type: 'json', nullable: true })
  labels: Record<string, string>;

  @Column({ length: 20, default: 'gauge' })
  type: 'gauge' | 'counter' | 'histogram' | 'summary';

  @Column({ length: 500, nullable: true })
  description: string;

  @CreateDateColumn()
  timestamp: Date;
}
