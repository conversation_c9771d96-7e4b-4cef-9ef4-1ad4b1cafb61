/**
 * 增强型Event Bus服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { EventBusService } from './event-bus.service';

/**
 * 事件优先级
 */
export enum EventPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

/**
 * 事件元数据
 */
export interface EventMetadata {
  priority: EventPriority;
  timestamp: Date;
  source: string;
  correlationId?: string;
}

/**
 * 增强型事件
 */
export interface EnhancedEvent {
  type: string;
  payload: any;
  metadata: EventMetadata;
}

@Injectable()
export class EnhancedEventBusService {
  private readonly logger = new Logger(EnhancedEventBusService.name);
  private readonly eventQueue: EnhancedEvent[] = [];
  private processing = false;

  constructor(private readonly eventBus: EventBusService) {}

  /**
   * 发布增强型事件
   */
  async emitEnhanced(
    type: string,
    payload: any,
    priority: EventPriority = EventPriority.NORMAL,
    source: string = 'unknown',
    correlationId?: string
  ): Promise<void> {
    const event: EnhancedEvent = {
      type,
      payload,
      metadata: {
        priority,
        timestamp: new Date(),
        source,
        correlationId
      }
    };

    this.eventQueue.push(event);
    this.eventQueue.sort((a, b) => b.metadata.priority - a.metadata.priority);

    if (!this.processing) {
      await this.processQueue();
    }
  }

  /**
   * 处理事件队列
   */
  private async processQueue(): Promise<void> {
    this.processing = true;

    while (this.eventQueue.length > 0) {
      const event = this.eventQueue.shift();
      if (event) {
        try {
          await this.eventBus.emit(event.type, {
            ...event.payload,
            metadata: event.metadata
          });
        } catch (error) {
          this.logger.error(`处理事件失败: ${event.type}`, error);
        }
      }
    }

    this.processing = false;
  }

  /**
   * 发布事件（别名）
   */
  async publish(
    type: string,
    payload: any,
    options?: { priority?: EventPriority; source?: string; correlationId?: string }
  ): Promise<void> {
    const priority = options?.priority || EventPriority.NORMAL;
    const source = options?.source || 'unknown';
    const correlationId = options?.correlationId;

    return this.emitEnhanced(type, payload, priority, source, correlationId);
  }

  /**
   * 订阅事件
   */
  on(event: string, listener: (...args: any[]) => void): void {
    this.eventBus.on(event, listener);
  }

  /**
   * 订阅事件（别名）
   */
  subscribe(event: string, listener: (...args: any[]) => void): void {
    this.on(event, listener);
  }

  /**
   * 取消订阅
   */
  off(event: string, listener: (...args: any[]) => void): void {
    this.eventBus.off(event, listener);
  }
}
