#!/bin/bash

# DL引擎Redis服务网络诊断脚本
# 用于诊断Docker Hub连接问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 检查基本网络连接
check_basic_network() {
    log_info "检查基本网络连接..."
    
    # 检查DNS解析
    if nslookup docker.io >/dev/null 2>&1; then
        log_success "DNS解析正常 (docker.io)"
    else
        log_error "DNS解析失败 (docker.io)"
        return 1
    fi
    
    # 检查Docker Hub连接
    if curl -s --connect-timeout 10 https://docker.io >/dev/null; then
        log_success "Docker Hub连接正常"
    else
        log_error "Docker Hub连接失败"
        return 1
    fi
    
    return 0
}

# 检查Docker配置
check_docker_config() {
    log_info "检查Docker配置..."
    
    # 检查Docker版本
    local docker_version=$(docker --version 2>/dev/null || echo "未安装")
    log_info "Docker版本: $docker_version"
    
    # 检查Docker守护进程状态
    if docker info >/dev/null 2>&1; then
        log_success "Docker守护进程运行正常"
    else
        log_error "Docker守护进程未运行"
        return 1
    fi
    
    # 检查Docker镜像仓库配置
    log_info "检查Docker镜像仓库配置..."
    docker info | grep -A 10 "Registry Mirrors" || log_info "未配置镜像加速器"
    
    return 0
}

# 测试镜像拉取
test_image_pull() {
    log_info "测试镜像拉取..."
    
    local test_images=("hello-world" "alpine:latest" "redis:alpine")
    
    for image in "${test_images[@]}"; do
        log_info "测试拉取镜像: $image"
        
        if timeout 60 docker pull "$image" >/dev/null 2>&1; then
            log_success "成功拉取: $image"
            docker rmi "$image" >/dev/null 2>&1 || true
        else
            log_error "拉取失败: $image"
        fi
    done
}

# 提供解决方案建议
suggest_solutions() {
    log_info "网络问题解决方案建议:"
    echo "========================================"
    
    echo "1. 配置Docker镜像加速器（推荐）:"
    echo "   - 阿里云: https://cr.console.aliyun.com/cn-hangzhou/instances/mirrors"
    echo "   - 腾讯云: https://cloud.tencent.com/document/product/1141/50332"
    echo "   - 网易云: http://hub-mirror.c.163.com"
    echo ""
    
    echo "2. 修改Docker配置文件 (~/.docker/daemon.json):"
    echo '   {'
    echo '     "registry-mirrors": ['
    echo '       "https://docker.mirrors.ustc.edu.cn",'
    echo '       "https://hub-mirror.c.163.com",'
    echo '       "https://mirror.baidubce.com"'
    echo '     ]'
    echo '   }'
    echo ""
    
    echo "3. 重启Docker服务:"
    echo "   sudo systemctl restart docker  # Linux"
    echo "   或重启Docker Desktop           # Windows/Mac"
    echo ""
    
    echo "4. 使用代理服务器:"
    echo "   docker build --build-arg HTTP_PROXY=http://proxy:port ."
    echo ""
    
    echo "5. 离线构建方案:"
    echo "   - 预先下载Redis镜像"
    echo "   - 使用本地镜像仓库"
    echo "   - 使用多阶段构建"
    echo ""
    
    echo "6. 网络故障排查:"
    echo "   - 检查防火墙设置"
    echo "   - 检查代理配置"
    echo "   - 检查DNS设置"
    echo "   - 尝试使用VPN"
    echo "========================================"
}

# 创建Docker镜像加速器配置
create_mirror_config() {
    log_info "创建Docker镜像加速器配置..."
    
    local config_dir="$HOME/.docker"
    local config_file="$config_dir/daemon.json"
    
    # 创建配置目录
    mkdir -p "$config_dir"
    
    # 创建配置文件
    cat > "$config_file" << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://dockerhub.azk8s.cn"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}
EOF
    
    log_success "Docker镜像加速器配置已创建: $config_file"
    log_warning "请重启Docker服务以使配置生效"
}

# 测试镜像加速器
test_mirror_speed() {
    log_info "测试镜像加速器速度..."
    
    local mirrors=(
        "docker.io"
        "docker.mirrors.ustc.edu.cn"
        "hub-mirror.c.163.com"
        "mirror.baidubce.com"
    )
    
    for mirror in "${mirrors[@]}"; do
        log_info "测试镜像源: $mirror"
        
        local start_time=$(date +%s)
        if timeout 30 curl -s "https://$mirror" >/dev/null 2>&1; then
            local end_time=$(date +%s)
            local duration=$((end_time - start_time))
            log_success "$mirror 响应时间: ${duration}秒"
        else
            log_error "$mirror 连接失败"
        fi
    done
}

# 主诊断函数
main() {
    log_info "开始Docker网络诊断..."
    
    echo "========================================"
    echo "DL引擎Redis服务网络诊断报告"
    echo "诊断时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================"
    
    # 基本网络检查
    if check_basic_network; then
        log_success "基本网络连接正常"
    else
        log_error "基本网络连接异常"
    fi
    
    echo ""
    
    # Docker配置检查
    if check_docker_config; then
        log_success "Docker配置正常"
    else
        log_error "Docker配置异常"
    fi
    
    echo ""
    
    # 测试镜像拉取
    test_image_pull
    
    echo ""
    
    # 测试镜像加速器速度
    test_mirror_speed
    
    echo ""
    
    # 提供解决方案建议
    suggest_solutions
    
    log_info "网络诊断完成"
}

# 显示帮助信息
show_help() {
    echo "DL引擎Redis服务网络诊断脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  check                   执行网络诊断（默认）"
    echo "  mirror                  创建镜像加速器配置"
    echo "  test                    测试镜像拉取"
    echo "  speed                   测试镜像源速度"
    echo "  help                    显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                      # 执行完整诊断"
    echo "  $0 mirror               # 创建镜像加速器配置"
    echo "  $0 test                 # 测试镜像拉取"
}

# 处理命令行参数
case "${1:-}" in
    "check"|"")
        main
        ;;
    "mirror")
        create_mirror_config
        ;;
    "test")
        test_image_pull
        ;;
    "speed")
        test_mirror_speed
        ;;
    "help"|"-h"|"--help")
        show_help
        exit 0
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
