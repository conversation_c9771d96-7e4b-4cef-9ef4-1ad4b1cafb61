# DL引擎MySQL配置文件
# 针对微服务架构优化的MySQL配置

[mysqld]
# 基本设置
user = mysql
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid
datadir = /var/lib/mysql
tmpdir = /tmp

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# 时区设置
default-time-zone = '+08:00'

# SQL模式设置（兼容性优化）
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 连接设置
max_connections = 500
max_connect_errors = 100000
max_user_connections = 450
back_log = 200

# 超时设置
wait_timeout = 28800
interactive_timeout = 28800
connect_timeout = 10
net_read_timeout = 30
net_write_timeout = 60

# 内存设置
# 根据容器内存调整，建议容器至少2GB内存
key_buffer_size = 256M
max_allowed_packet = 64M
table_open_cache = 2000
sort_buffer_size = 2M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
myisam_sort_buffer_size = 64M
thread_cache_size = 50
query_cache_size = 0
query_cache_type = 0

# InnoDB设置
default-storage-engine = INNODB
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_lock_wait_timeout = 50
innodb_file_per_table = 1
innodb_open_files = 2000
innodb_io_capacity = 2000
innodb_flush_method = O_DIRECT

# 二进制日志设置
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M
sync_binlog = 0

# 错误日志
log-error = /var/log/mysql/error.log

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# 通用查询日志（开发环境可开启）
# general_log = 1
# general_log_file = /var/log/mysql/general.log

# 安全设置
skip-name-resolve
skip-external-locking

# 性能优化
tmp_table_size = 64M
max_heap_table_size = 64M
bulk_insert_buffer_size = 8M

# 复制设置（为主从复制预留）
server-id = 1
gtid_mode = ON
enforce_gtid_consistency = ON

# 其他设置
lower_case_table_names = 1
explicit_defaults_for_timestamp = 1

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock

[mysqldump]
quick
quote-names
max_allowed_packet = 64M

[isamchk]
key_buffer_size = 256M
sort_buffer_size = 256M
read_buffer = 2M
write_buffer = 2M
