#!/bin/bash

# DL引擎MySQL Docker入口脚本
# 设置安全的环境变量并启动MySQL

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 设置MySQL安全环境变量
setup_mysql_env() {
    log_info "设置MySQL安全环境变量..."
    
    # 设置MySQL安全配置
    export MYSQL_ALLOW_EMPTY_PASSWORD=no
    export MYSQL_RANDOM_ROOT_PASSWORD=no
    
    # 验证必需的环境变量
    if [ -z "$MYSQL_ROOT_PASSWORD" ]; then
        log_error "MYSQL_ROOT_PASSWORD环境变量未设置"
        log_error "请在docker run命令中设置: -e MYSQL_ROOT_PASSWORD=your_secure_password"
        exit 1
    fi
    
    # 验证密码强度
    local password_length=${#MYSQL_ROOT_PASSWORD}
    if [ $password_length -lt 8 ]; then
        log_warning "MySQL root密码长度少于8位，建议使用更强的密码"
    fi
    
    # 检查是否使用默认密码
    if [ "$MYSQL_ROOT_PASSWORD" = "dl_engine_password_2024" ] || [ "$MYSQL_ROOT_PASSWORD" = "your_secure_password_here" ]; then
        log_warning "检测到使用默认密码，强烈建议修改为自定义安全密码"
    fi
    
    log_success "MySQL环境变量设置完成"
}

# 验证MySQL配置
validate_mysql_config() {
    log_info "验证MySQL配置..."
    
    # 检查配置文件
    if [ -f "/etc/mysql/conf.d/dl-engine.cnf" ]; then
        log_success "DL引擎MySQL配置文件已加载"
    else
        log_warning "DL引擎MySQL配置文件未找到"
    fi
    
    # 检查初始化脚本
    if [ -d "/docker-entrypoint-initdb.d" ] && [ "$(ls -A /docker-entrypoint-initdb.d)" ]; then
        log_success "数据库初始化脚本已准备"
        ls -la /docker-entrypoint-initdb.d/
    else
        log_warning "数据库初始化脚本目录为空"
    fi
    
    log_success "MySQL配置验证完成"
}

# 显示启动信息
show_startup_info() {
    log_info "DL引擎MySQL服务启动信息:"
    echo "========================================"
    echo "MySQL版本: $(mysqld --version 2>/dev/null | head -1 || echo '未知')"
    echo "配置文件: /etc/mysql/conf.d/dl-engine.cnf"
    echo "数据目录: ${MYSQL_DATA_DIR:-/var/lib/mysql}"
    echo "启动时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "时区: ${TZ:-UTC}"
    echo "字符集: ${MYSQL_CHARSET:-utf8mb4}"
    echo "排序规则: ${MYSQL_COLLATION:-utf8mb4_unicode_ci}"
    echo "========================================"
}

# 主函数
main() {
    log_info "开始DL引擎MySQL服务启动..."
    
    # 设置MySQL环境变量
    setup_mysql_env
    
    # 验证MySQL配置
    validate_mysql_config
    
    # 显示启动信息
    show_startup_info
    
    log_success "MySQL服务启动准备完成"
    log_info "正在启动MySQL服务器..."
    
    # 调用原始的MySQL入口脚本
    exec /usr/local/bin/docker-entrypoint.sh "$@"
}

# 如果脚本被直接执行
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
