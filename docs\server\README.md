# DL引擎服务端使用文档

本文档详细描述了DL（Digital Learning）引擎的服务端架构、微服务启动顺序和使用方法。

## 🏗️ 架构概述

DL引擎采用微服务架构，包含以下核心服务：

### 基础设施层
- **MySQL数据库**：主数据存储（端口：3306）
- **Redis缓存**：缓存和会话存储（端口：6379）

### 核心微服务层
1. **服务注册中心（service-registry）**：服务发现和注册（端口：3010/4010）
2. **API网关（api-gateway）**：统一入口，路由和认证（端口：3000）
3. **用户服务（user-service）**：用户管理和认证（端口：3001/4001）
4. **项目服务（project-service）**：项目和场景管理（端口：3002/4002）
5. **资产服务（asset-service）**：资产文件管理（端口：3003/4003）
6. **渲染服务（render-service）**：3D渲染和图像处理（端口：3004/4004）
7. **协作服务（collaboration-service）**：实时协作功能（端口：3005/3006/3007）
8. **游戏服务器（game-server）**：游戏实例管理（端口：3030）

### 监控和运维层
- **监控服务（monitoring-service）**：系统监控和告警（端口：3100）
- **Prometheus**：指标收集（端口：9090）
- **Grafana**：监控仪表板（端口：3000）
- **ELK Stack**：日志分析（Elasticsearch:9200, Kibana:5601）

### 前端层
- **编辑器（editor）**：Web编辑器界面（端口：80）

## 🚀 微服务启动顺序

### 第一层：基础设施（必须首先启动）
```bash
# 1. MySQL数据库
docker-compose up -d mysql

# 2. Redis缓存
docker-compose up -d redis
```

**等待条件**：MySQL和Redis健康检查通过

### 第二层：服务注册中心（核心依赖）
```bash
# 3. 服务注册中心
docker-compose up -d service-registry
```

**依赖关系**：
- 依赖：MySQL（数据存储）
- 健康检查：`http://localhost:4010/health`

### 第三层：业务微服务（并行启动）
```bash
# 4. 用户服务
docker-compose up -d user-service

# 5. 项目服务  
docker-compose up -d project-service

# 6. 资产服务
docker-compose up -d asset-service

# 7. 渲染服务
docker-compose up -d render-service
```

**依赖关系**：
- 依赖：MySQL + 服务注册中心
- 健康检查：各自的`/health`端点

### 第四层：协作服务（负载均衡）
```bash
# 8. 协作服务实例1
docker-compose up -d collaboration-service-1

# 9. 协作服务实例2  
docker-compose up -d collaboration-service-2

# 10. 协作服务负载均衡器
docker-compose up -d collaboration-load-balancer
```

**依赖关系**：
- 依赖：Redis + 服务注册中心
- 负载均衡：Nginx代理两个实例

### 第五层：API网关（统一入口）
```bash
# 11. API网关
docker-compose up -d api-gateway
```

**依赖关系**：
- 依赖：服务注册中心 + 所有业务微服务
- 健康检查：`http://localhost:3000/api/health`

### 第六层：前端应用
```bash
# 12. 编辑器前端
docker-compose up -d editor
```

**依赖关系**：
- 依赖：API网关 + 协作服务负载均衡器
- 访问地址：`http://localhost`

### 第七层：监控服务（可选）
```bash
# 13. 监控服务
docker-compose -f docker-compose.monitoring.yml up -d
```

## 📋 完整启动命令

### 方式一：一键启动（推荐）
```bash
# 启动所有服务（Docker会自动处理依赖顺序）
docker-compose up -d

# 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d
```

### 方式二：分步启动（开发调试）
```bash
# 第一步：基础设施
docker-compose up -d mysql redis

# 等待基础设施就绪
sleep 30

# 第二步：服务注册中心
docker-compose up -d service-registry

# 等待服务注册中心就绪
sleep 15

# 第三步：业务微服务
docker-compose up -d user-service project-service asset-service render-service

# 等待业务服务就绪
sleep 20

# 第四步：协作服务
docker-compose up -d collaboration-service-1 collaboration-service-2
sleep 10
docker-compose up -d collaboration-load-balancer

# 第五步：API网关
docker-compose up -d api-gateway

# 第六步：前端
docker-compose up -d editor
```

### 方式三：开发模式启动
```bash
# 启动基础设施
docker-compose up -d mysql redis

# 手动启动各个微服务（用于开发调试）
cd server/service-registry && npm run start:dev &
cd server/user-service && npm run start:dev &
cd server/project-service && npm run start:dev &
cd server/asset-service && npm run start:dev &
cd server/render-service && npm run start:dev &
cd server/collaboration-service && npm run start:dev &
cd server/api-gateway && npm run start:dev &
cd server/monitoring-service && npm run start:dev &
```

## 🔍 服务详细信息

### 1. 服务注册中心（service-registry）
```yaml
端口配置:
  微服务端口: 3010
  HTTP端口: 4010
功能:
  - 服务注册与发现
  - 负载均衡策略
  - 健康检查管理
  - 服务缓存
健康检查: GET http://localhost:4010/health
API文档: GET http://localhost:4010/api/docs
```

### 2. API网关（api-gateway）
```yaml
端口配置:
  HTTP端口: 3000
功能:
  - 统一API入口
  - 请求路由转发
  - JWT认证授权
  - 限流和熔断
健康检查: GET http://localhost:3000/api/health
API文档: GET http://localhost:3000/api/docs
```

### 3. 用户服务（user-service）
```yaml
端口配置:
  微服务端口: 3001
  HTTP端口: 4001
功能:
  - 用户注册登录
  - 用户信息管理
  - 角色权限管理
  - JWT令牌生成
数据库: ir_engine_users
健康检查: GET http://localhost:4001/health
```

### 4. 项目服务（project-service）
```yaml
端口配置:
  微服务端口: 3002
  HTTP端口: 4002
功能:
  - 项目创建管理
  - 场景管理
  - 项目权限控制
  - 版本管理
数据库: ir_engine_projects
健康检查: GET http://localhost:4002/health
```

### 5. 资产服务（asset-service）
```yaml
端口配置:
  微服务端口: 3003
  HTTP端口: 4003
功能:
  - 文件上传下载
  - 资产分类管理
  - 缩略图生成
  - 存储管理
数据库: ir_engine_assets
存储卷: asset_uploads
健康检查: GET http://localhost:4003/health
```

### 6. 渲染服务（render-service）
```yaml
端口配置:
  微服务端口: 3004
  HTTP端口: 4004
功能:
  - 3D场景渲染
  - 图像处理
  - 渲染队列管理
  - 输出文件管理
数据库: ir_engine_render
存储卷: render_outputs
健康检查: GET http://localhost:4004/health
```

### 7. 协作服务（collaboration-service）
```yaml
端口配置:
  实例1端口: 3005
  实例2端口: 3006
  负载均衡端口: 3007
功能:
  - 实时协作编辑
  - WebSocket通信
  - 操作同步
  - 冲突解决
负载均衡: Nginx (IP Hash)
健康检查: GET http://localhost:3007/health
```

### 8. 游戏服务器（game-server）
```yaml
端口配置:
  HTTP端口: 3030
  微服务端口: 3031
功能:
  - 游戏实例管理
  - Agones集成
  - WebRTC通信
  - 实例调度
健康检查: GET http://localhost:3030/api/health
```

### 9. 监控服务（monitoring-service）
```yaml
端口配置:
  HTTP端口: 3100
功能:
  - 系统监控
  - 告警通知
  - 日志分析
  - 性能指标
健康检查: GET http://localhost:3100/api/v1/health
```

## 🔧 环境变量配置

创建`.env`文件：

```env
# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_here

# 服务配置
NODE_ENV=production

# 网络配置
CORS_ORIGIN=*

# 文件上传配置
MAX_FILE_SIZE=104857600

# Redis配置（可选）
REDIS_PASSWORD=your_redis_password

# 监控配置
ELASTICSEARCH_ENABLED=true
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_mail_password
```

## 📊 监控和健康检查

### 健康检查端点
```bash
# 检查所有服务状态
curl http://localhost:3000/api/health

# 检查各个服务
curl http://localhost:4010/health  # 服务注册中心
curl http://localhost:4001/health  # 用户服务
curl http://localhost:4002/health  # 项目服务
curl http://localhost:4003/health  # 资产服务
curl http://localhost:4004/health  # 渲染服务
curl http://localhost:3007/health  # 协作服务
```

### 监控面板
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Kibana**: http://localhost:5601
- **监控服务**: http://localhost:3100

### 服务状态查看
```bash
# 查看所有容器状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service-name]

# 查看资源使用情况
docker stats
```

## 🛠️ 故障排除

### 常见启动问题

#### 1. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :3000

# 停止冲突服务
sudo kill -9 $(lsof -t -i:3000)
```

#### 2. 数据库连接失败
```bash
# 检查MySQL状态
docker-compose logs mysql

# 重启MySQL
docker-compose restart mysql

# 检查数据库连接
docker exec -it dl-engine-mysql mysql -u root -p
```

#### 3. 服务注册失败
```bash
# 检查服务注册中心日志
docker-compose logs service-registry

# 重启服务注册中心
docker-compose restart service-registry

# 检查网络连接
docker network ls
docker network inspect dl-engine-network
```

#### 4. 内存不足
```bash
# 检查系统资源
free -h
df -h

# 清理Docker资源
docker system prune -a
docker volume prune
```

### 服务重启顺序
如果需要重启服务，请按以下顺序：

```bash
# 1. 停止前端和网关
docker-compose stop editor api-gateway

# 2. 停止业务服务
docker-compose stop collaboration-load-balancer collaboration-service-1 collaboration-service-2
docker-compose stop render-service asset-service project-service user-service

# 3. 停止服务注册中心
docker-compose stop service-registry

# 4. 重启基础设施（如需要）
docker-compose restart mysql redis

# 5. 按启动顺序重新启动
docker-compose up -d service-registry
sleep 10
docker-compose up -d user-service project-service asset-service render-service
sleep 15
docker-compose up -d collaboration-service-1 collaboration-service-2
sleep 5
docker-compose up -d collaboration-load-balancer
sleep 5
docker-compose up -d api-gateway
sleep 5
docker-compose up -d editor
```

## 📚 API文档和接口

### 主要API端点

#### 认证相关
```http
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册
POST /api/auth/refresh        # 刷新令牌
GET  /api/auth/profile        # 获取用户信息
```

#### 项目管理
```http
GET    /api/projects          # 获取项目列表
POST   /api/projects          # 创建项目
GET    /api/projects/:id      # 获取项目详情
PUT    /api/projects/:id      # 更新项目
DELETE /api/projects/:id      # 删除项目
```

#### 场景管理
```http
GET    /api/projects/:id/scenes     # 获取场景列表
POST   /api/projects/:id/scenes     # 创建场景
GET    /api/scenes/:id              # 获取场景详情
PUT    /api/scenes/:id              # 更新场景
DELETE /api/scenes/:id              # 删除场景
```

#### 资产管理
```http
GET    /api/assets            # 获取资产列表
POST   /api/assets/upload     # 上传资产
GET    /api/assets/:id        # 获取资产详情
DELETE /api/assets/:id        # 删除资产
```

#### 渲染服务
```http
POST   /api/render/jobs       # 创建渲染任务
GET    /api/render/jobs/:id   # 获取渲染状态
GET    /api/render/outputs    # 获取渲染输出
```

### 完整API文档
启动服务后访问：
- **API网关文档**: http://localhost:3000/api/docs
- **服务注册中心文档**: http://localhost:4010/api/docs

## 🔐 安全配置

### JWT认证
```javascript
// JWT配置示例
{
  "secret": "your-secret-key",
  "expiresIn": "1d",
  "algorithm": "HS256"
}
```

### CORS配置
```javascript
// CORS配置
{
  "origin": ["http://localhost:3000", "http://localhost"],
  "methods": ["GET", "POST", "PUT", "DELETE"],
  "credentials": true
}
```

### 安全头设置
```javascript
// Helmet安全头
{
  "contentSecurityPolicy": true,
  "crossOriginEmbedderPolicy": true,
  "crossOriginOpenerPolicy": true,
  "crossOriginResourcePolicy": true
}
```

## 💾 数据备份和恢复

### 数据库备份
```bash
# 备份所有数据库
docker exec dl-engine-mysql mysqldump -u root -p --all-databases > backup_all.sql

# 备份特定数据库
docker exec dl-engine-mysql mysqldump -u root -p ir_engine_users > backup_users.sql
docker exec dl-engine-mysql mysqldump -u root -p ir_engine_projects > backup_projects.sql
docker exec dl-engine-mysql mysqldump -u root -p ir_engine_assets > backup_assets.sql
```

### 数据恢复
```bash
# 恢复数据库
docker exec -i dl-engine-mysql mysql -u root -p ir_engine_users < backup_users.sql
```

### 文件备份
```bash
# 备份上传文件
docker cp dl-engine-asset-service:/app/uploads ./backup/uploads

# 备份渲染输出
docker cp dl-engine-render-service:/app/renders ./backup/renders

# 恢复文件
docker cp ./backup/uploads dl-engine-asset-service:/app/uploads
docker cp ./backup/renders dl-engine-render-service:/app/renders
```

## 🚀 性能优化建议

### 1. 数据库优化
- 配置MySQL连接池
- 添加适当的索引
- 定期清理日志表

### 2. 缓存策略
- Redis缓存热点数据
- 服务注册中心缓存
- 静态资源CDN

### 3. 负载均衡
- 协作服务多实例部署
- API网关请求分发
- 数据库读写分离

### 4. 监控告警
- 设置资源使用阈值
- 配置服务健康检查
- 日志聚合分析

## 📞 技术支持

如遇到问题，请按以下步骤排查：

1. 检查服务状态：`docker-compose ps`
2. 查看服务日志：`docker-compose logs [service-name]`
3. 检查健康检查端点
4. 验证环境变量配置
5. 检查网络连接和端口

更多详细信息请参考各个微服务的具体文档。

## 📋 服务启动检查清单

### 启动前检查
- [ ] Docker和Docker Compose已安装
- [ ] 端口3000-4010, 6379, 3306, 80未被占用
- [ ] 系统内存至少4GB可用
- [ ] 磁盘空间至少10GB可用
- [ ] 环境变量文件`.env`已配置

### 启动后验证
- [ ] MySQL数据库连接正常
- [ ] Redis缓存服务正常
- [ ] 服务注册中心健康检查通过
- [ ] 所有微服务注册成功
- [ ] API网关路由正常
- [ ] 前端页面可访问
- [ ] 监控服务数据正常

### 功能测试
- [ ] 用户注册登录功能
- [ ] 项目创建和管理
- [ ] 文件上传下载
- [ ] 实时协作功能
- [ ] 渲染服务正常

## 🔄 版本更新流程

### 1. 停止服务
```bash
docker-compose down
```

### 2. 备份数据
```bash
# 备份数据库
docker exec dl-engine-mysql mysqldump -u root -p --all-databases > backup_$(date +%Y%m%d).sql

# 备份文件
docker cp dl-engine-asset-service:/app/uploads ./backup/uploads_$(date +%Y%m%d)
```

### 3. 更新代码
```bash
git pull origin main
```

### 4. 重新构建
```bash
docker-compose build --no-cache
```

### 5. 启动服务
```bash
docker-compose up -d
```

### 6. 验证更新
```bash
# 检查服务状态
docker-compose ps

# 检查健康状态
curl http://localhost:3000/api/health
```

## 🐛 常见错误代码

### HTTP状态码
- **500**: 服务内部错误，检查服务日志
- **502**: 网关错误，检查上游服务状态
- **503**: 服务不可用，检查服务健康状态
- **504**: 网关超时，检查服务响应时间

### 微服务错误
- **CONNECTION_REFUSED**: 服务未启动或端口错误
- **SERVICE_NOT_FOUND**: 服务未在注册中心注册
- **DATABASE_CONNECTION_ERROR**: 数据库连接失败
- **REDIS_CONNECTION_ERROR**: Redis连接失败

## 📈 监控指标说明

### 系统指标
- **CPU使用率**: 建议保持在80%以下
- **内存使用率**: 建议保持在85%以下
- **磁盘使用率**: 建议保持在90%以下
- **网络I/O**: 监控带宽使用情况

### 应用指标
- **请求响应时间**: 平均响应时间应在200ms以下
- **错误率**: 应保持在1%以下
- **并发用户数**: 监控同时在线用户数
- **数据库连接数**: 监控连接池使用情况

### 业务指标
- **用户注册数**: 每日新增用户数
- **项目创建数**: 每日新增项目数
- **文件上传量**: 每日上传文件大小
- **渲染任务数**: 每日渲染任务数量

## 📚 相关文档

- [快速开始指南](QUICK_START.md) - 快速部署和启动指南
- [性能优化指南](performance-optimization.md) - 系统性能优化建议
- [协作服务功能总结](collaboration-service-summary.md) - 协作服务功能快速概览
- [协作服务功能详细分析](collaboration-service-analysis.md) - 协作服务架构和功能详解
- [游戏服务器功能总结](game-server-summary.md) - 游戏服务器功能快速概览
- [游戏服务器功能详细分析](game-server-analysis.md) - 游戏服务器架构和功能详解
- [服务注册中心功能总结](service-registry-summary.md) - 服务注册中心功能快速概览
- [服务注册中心功能详细分析](service-registry-analysis.md) - 服务注册中心架构和功能详解
- [API网关功能总结](api-gateway-summary.md) - API网关功能快速概览
- [API网关功能详细分析](api-gateway-analysis.md) - API网关架构和功能详解

---

**注意**: 本文档会随着系统更新而持续更新，请定期查看最新版本。
