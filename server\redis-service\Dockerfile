# DL引擎Redis缓存服务
# 基于Redis 7.0，为DL引擎微服务架构提供统一的缓存和消息队列服务

FROM redis:alpine

# 维护者信息
LABEL maintainer="DL Engine Team"
LABEL description="DL引擎统一Redis缓存服务"
LABEL version="1.0.0"

# 安装必要的工具
RUN apk add --no-cache \
    bash \
    curl \
    tzdata \
    procps \
    net-tools

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建Redis用户和目录
RUN addgroup -g 999 redis && \
    adduser -D -u 999 -G redis redis

# 创建必要的目录
RUN mkdir -p /usr/local/etc/redis \
             /var/lib/redis \
             /var/log/redis \
             /usr/local/bin/scripts

# 复制Redis配置文件
COPY config/redis.conf /usr/local/etc/redis/redis.conf

# 复制启动脚本
COPY scripts/start-redis.sh /usr/local/bin/start-redis.sh

# 复制健康检查脚本
COPY scripts/health-check.sh /usr/local/bin/health-check.sh

# 复制监控脚本
COPY scripts/monitor.sh /usr/local/bin/monitor.sh

# 复制备份脚本
COPY scripts/backup.sh /usr/local/bin/backup.sh

# 复制恢复脚本
COPY scripts/restore.sh /usr/local/bin/restore.sh

# 复制清理脚本
COPY scripts/cleanup.sh /usr/local/bin/cleanup.sh

# 设置脚本权限
RUN chmod +x /usr/local/bin/*.sh

# 设置目录权限
RUN chown -R redis:redis /var/lib/redis /var/log/redis /usr/local/etc/redis

# 创建数据卷挂载点
VOLUME ["/var/lib/redis", "/var/log/redis"]

# 暴露Redis端口
EXPOSE 6379

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD /usr/local/bin/health-check.sh

# 切换到redis用户
USER redis

# 设置工作目录
WORKDIR /var/lib/redis

# 启动Redis服务
CMD ["/usr/local/bin/start-redis.sh"]
