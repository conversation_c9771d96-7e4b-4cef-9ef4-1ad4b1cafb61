#!/bin/bash

# ================================
# DL引擎 Docker Compose 快速部署脚本
# ================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用"
        return 1
    fi
    return 0
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Docker
    check_command docker
    
    # 检查Docker Compose
    check_command docker-compose
    
    # 检查Docker服务状态
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    # 检查系统资源
    local total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    if [ $total_mem -lt 4 ]; then
        log_warning "系统内存少于4GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    local available_space=$(df . | awk 'NR==2 {print $4}')
    if [ $available_space -lt 10485760 ]; then  # 10GB in KB
        log_warning "可用磁盘空间少于10GB，可能影响运行"
    fi
    
    log_success "系统要求检查完成"
}

# 检查关键端口
check_ports() {
    log_info "检查端口占用情况..."
    
    local ports=(80 3000 3001 3002 3003 3004 3005 3006 3007 3010 3030 3031 3306 6379 9000 9001)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if ! check_port $port; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        log_warning "以下端口被占用: ${occupied_ports[*]}"
        read -p "是否继续部署？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi
    
    log_success "端口检查完成"
}

# 创建环境变量文件
setup_env() {
    log_info "设置环境变量..."
    
    if [ ! -f .env ]; then
        if [ -f docs/deployment/.env.example ]; then
            cp docs/deployment/.env.example .env
            log_success "已创建 .env 文件"
        else
            log_warning ".env.example 文件不存在，创建默认配置"
            cat > .env << 'EOF'
# DL引擎基础配置
NODE_ENV=production
COMPOSE_PROJECT_NAME=dl-engine

# 数据库配置
MYSQL_ROOT_PASSWORD=dl_engine_password_2024
MYSQL_USER=dl_user
MYSQL_PASSWORD=dl_user_password_2024

# JWT配置
JWT_SECRET=dl_engine_jwt_secret_key_2024_very_secure_and_long

# 网络配置
CORS_ORIGIN=*
API_GATEWAY_PORT=3000
EDITOR_PORT=80

# MinIO配置
MINIO_ROOT_USER=dl_engine_minio_admin
MINIO_ROOT_PASSWORD=dl_engine_minio_password_2024
MINIO_ACCESS_KEY=dl_engine_access_key
MINIO_SECRET_KEY=dl_engine_secret_key_2024

# Redis配置
REDIS_PASSWORD=dl_engine_redis_password_2024

# 文件上传配置
MAX_FILE_SIZE=104857600

# 协作服务配置
ENABLE_COMPRESSION=true
COMPRESSION_LEVEL=6
MAX_BATCH_SIZE=50
MAX_BATCH_WAIT_TIME=50

# 监控配置
ELASTICSEARCH_ENABLED=true
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin_password_2024

# 邮件配置
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_mail_password
MAIL_FROM=<EMAIL>
EOF
        fi
        
        log_warning "请编辑 .env 文件，修改默认密码和配置"
        read -p "是否现在编辑 .env 文件？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            ${EDITOR:-nano} .env
        fi
    else
        log_success ".env 文件已存在"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    local dirs=(
        "data/mysql"
        "data/redis"
        "data/minio"
        "data/elasticsearch"
        "data/prometheus"
        "data/grafana"
        "logs"
        "backups"
        "config/mysql"
        "config/redis"
        "config/nginx"
        "config/prometheus"
        "config/grafana"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
    done
    
    # 设置目录权限
    chmod -R 755 data/
    chmod -R 755 logs/
    chmod -R 755 backups/
    
    log_success "目录创建完成"
}

# 拉取Docker镜像
pull_images() {
    log_info "拉取Docker镜像..."
    
    # 使用完整的compose文件
    if [ -f docs/deployment/docker-compose-complete.yml ]; then
        docker-compose -f docs/deployment/docker-compose-complete.yml pull
    else
        docker-compose pull
    fi
    
    log_success "Docker镜像拉取完成"
}

# 构建自定义镜像
build_images() {
    log_info "构建自定义镜像..."
    
    # 使用完整的compose文件
    if [ -f docs/deployment/docker-compose-complete.yml ]; then
        docker-compose -f docs/deployment/docker-compose-complete.yml build
    else
        docker-compose build
    fi
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 使用完整的compose文件
    local compose_file="docker-compose.yml"
    if [ -f docs/deployment/docker-compose-complete.yml ]; then
        compose_file="docs/deployment/docker-compose-complete.yml"
    fi
    
    # 启动基础设施服务
    log_info "启动基础设施服务..."
    docker-compose -f $compose_file up -d mysql redis minio
    
    # 等待基础设施服务就绪
    log_info "等待基础设施服务就绪..."
    sleep 30
    
    # 启动MinIO初始化
    if docker-compose -f $compose_file ps | grep -q minio-init; then
        docker-compose -f $compose_file up minio-init
    fi
    
    # 启动核心微服务
    log_info "启动核心微服务..."
    docker-compose -f $compose_file up -d service-registry
    sleep 15
    
    docker-compose -f $compose_file up -d user-service project-service asset-service render-service
    sleep 20
    
    # 启动协作服务
    log_info "启动协作服务..."
    docker-compose -f $compose_file up -d collaboration-service-1 collaboration-service-2 collaboration-load-balancer
    sleep 15
    
    # 启动API网关
    log_info "启动API网关..."
    docker-compose -f $compose_file up -d api-gateway
    sleep 10
    
    # 启动前端编辑器
    log_info "启动前端编辑器..."
    docker-compose -f $compose_file up -d editor
    
    # 启动游戏服务器（可选）
    if docker-compose -f $compose_file config | grep -q game-server; then
        log_info "启动游戏服务器..."
        docker-compose -f $compose_file up -d game-server
    fi
    
    log_success "所有服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    local services=(
        "localhost:4010/health"
        "localhost:4001/health"
        "localhost:4002/health"
        "localhost:4003/health"
        "localhost:4004/health"
        "localhost:3000/api/health"
        "localhost:80"
    )
    
    for service in "${services[@]}"; do
        local url="http://$service"
        local max_attempts=30
        local attempt=1
        
        log_info "等待 $service 就绪..."
        
        while [ $attempt -le $max_attempts ]; do
            if curl -f -s "$url" >/dev/null 2>&1; then
                log_success "$service 已就绪"
                break
            fi
            
            if [ $attempt -eq $max_attempts ]; then
                log_warning "$service 未能在预期时间内就绪"
                break
            fi
            
            sleep 5
            ((attempt++))
        done
    done
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    # 检查容器状态
    log_info "检查容器状态..."
    docker-compose ps
    
    # 检查服务健康状态
    log_info "检查服务健康状态..."
    
    local health_checks=(
        "http://localhost:3000/api/health"
        "http://localhost:4010/health"
        "http://localhost:4001/health"
        "http://localhost:4002/health"
        "http://localhost:4003/health"
        "http://localhost:4004/health"
    )
    
    local failed_checks=0
    
    for check in "${health_checks[@]}"; do
        if curl -f -s "$check" >/dev/null 2>&1; then
            log_success "✓ $check"
        else
            log_error "✗ $check"
            ((failed_checks++))
        fi
    done
    
    if [ $failed_checks -eq 0 ]; then
        log_success "所有健康检查通过"
    else
        log_warning "$failed_checks 个服务健康检查失败"
    fi
}

# 显示访问信息
show_access_info() {
    log_success "部署完成！"
    echo
    echo "================================"
    echo "访问地址："
    echo "================================"
    echo "编辑器主页:     http://localhost"
    echo "API网关:       http://localhost:3000/api"
    echo "API文档:       http://localhost:3000/api/docs"
    echo "MinIO控制台:   http://localhost:9001"
    echo
    echo "================================"
    echo "管理命令："
    echo "================================"
    echo "查看服务状态:   docker-compose ps"
    echo "查看日志:       docker-compose logs -f"
    echo "停止服务:       docker-compose down"
    echo "重启服务:       docker-compose restart"
    echo
    echo "================================"
    echo "默认登录信息："
    echo "================================"
    echo "MinIO控制台:"
    echo "  用户名: $(grep MINIO_ROOT_USER .env | cut -d'=' -f2)"
    echo "  密码:   $(grep MINIO_ROOT_PASSWORD .env | cut -d'=' -f2)"
    echo
    echo "注意: 请及时修改默认密码！"
}

# 主函数
main() {
    echo "================================"
    echo "DL引擎 Docker Compose 部署脚本"
    echo "================================"
    echo
    
    # 检查是否在正确的目录
    if [ ! -f "docker-compose.yml" ] && [ ! -f "docs/deployment/docker-compose-complete.yml" ]; then
        log_error "未找到 docker-compose.yml 文件，请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_requirements
    check_ports
    setup_env
    create_directories
    pull_images
    build_images
    start_services
    wait_for_services
    verify_deployment
    show_access_info
    
    log_success "部署脚本执行完成！"
}

# 处理中断信号
trap 'log_error "部署被中断"; exit 1' INT TERM

# 运行主函数
main "$@"
