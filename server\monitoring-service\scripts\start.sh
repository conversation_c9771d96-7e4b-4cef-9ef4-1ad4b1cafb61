#!/bin/bash

# 监控服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js版本
check_node_version() {
    log_info "检查Node.js版本..."
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本过低，需要18+，当前版本: $(node -v)"
        exit 1
    fi
    
    log_success "Node.js版本检查通过: $(node -v)"
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            log_warning ".env文件不存在，从.env.example复制..."
            cp .env.example .env
            log_warning "请编辑.env文件配置正确的环境变量"
        else
            log_error ".env文件不存在，请创建环境变量配置文件"
            exit 1
        fi
    fi
    
    log_success "环境变量检查完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖包..."
    
    if [ ! -d "node_modules" ]; then
        npm install --legacy-peer-deps
    else
        log_info "依赖包已存在，跳过安装"
    fi
    
    log_success "依赖包安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    npm run build
    
    log_success "项目构建完成"
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 这里可以添加数据库连接检查逻辑
    # 暂时跳过，在应用启动时会自动检查
    
    log_success "数据库连接检查完成"
}

# 启动服务
start_service() {
    local mode=${1:-"development"}
    
    log_info "启动监控服务 (模式: $mode)..."
    
    case $mode in
        "development"|"dev")
            npm run start:dev
            ;;
        "production"|"prod")
            npm run start:prod
            ;;
        "debug")
            npm run start:debug
            ;;
        *)
            log_error "未知的启动模式: $mode"
            log_info "支持的模式: development, production, debug"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    local mode=${1:-"development"}
    
    log_info "开始启动监控服务..."
    
    # 检查环境
    check_node_version
    check_environment
    
    # 安装依赖
    install_dependencies
    
    # 如果是生产模式，需要构建
    if [ "$mode" = "production" ] || [ "$mode" = "prod" ]; then
        build_project
    fi
    
    # 检查数据库
    check_database
    
    # 启动服务
    start_service "$mode"
}

# 显示帮助信息
show_help() {
    echo "监控服务启动脚本"
    echo ""
    echo "用法: $0 [模式]"
    echo ""
    echo "模式:"
    echo "  development, dev    开发模式 (默认)"
    echo "  production, prod    生产模式"
    echo "  debug              调试模式"
    echo ""
    echo "示例:"
    echo "  $0                 # 开发模式启动"
    echo "  $0 production      # 生产模式启动"
    echo "  $0 debug           # 调试模式启动"
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
