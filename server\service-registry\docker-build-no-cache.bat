@echo off
echo ================================
echo Service Registry Docker 构建脚本
echo 强制清理缓存并重新构建
echo ================================

echo.
echo 1. 清理Docker构建缓存...
docker builder prune -f

echo.
echo 2. 删除旧镜像...
docker rmi service-registry 2>nul

echo.
echo 3. 清理本地构建文件...
if exist node_modules rmdir /s /q node_modules
if exist dist rmdir /s /q dist

echo.
echo 4. 重新安装依赖...
npm install --legacy-peer-deps

echo.
echo 5. 本地构建测试...
npm run build

echo.
echo 6. 无缓存Docker构建...
docker build --no-cache -t service-registry .

echo.
echo 7. 验证镜像...
docker images | findstr service-registry

echo.
echo 构建完成！
pause
