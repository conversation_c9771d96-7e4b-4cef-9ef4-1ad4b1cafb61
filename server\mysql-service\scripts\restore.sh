#!/bin/bash

# DL引擎MySQL数据库恢复脚本
# 支持从备份文件恢复数据库

set -e

# 配置变量
MYSQL_HOST=${MYSQL_HOST:-"localhost"}
MYSQL_PORT=${MYSQL_PORT:-"3306"}
MYSQL_USER=${MYSQL_USER:-"root"}
MYSQL_PASSWORD=${MYSQL_ROOT_PASSWORD:-"dl_engine_password_2024"}
BACKUP_DIR=${BACKUP_DIR:-"/var/lib/mysql-files/backups"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    
    if mysqladmin ping -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" --silent 2>/dev/null; then
        log_success "MySQL连接正常"
        return 0
    else
        log_error "MySQL连接失败"
        return 1
    fi
}

# 列出可用的备份文件
list_backups() {
    log_info "可用的备份文件:"
    echo "========================================"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "备份目录不存在: $BACKUP_DIR"
        return 1
    fi
    
    local backup_files=$(find "$BACKUP_DIR" -name "*.sql.gz" -type f | sort -r)
    
    if [ -z "$backup_files" ]; then
        log_warning "未找到备份文件"
        return 1
    fi
    
    local count=1
    echo "$backup_files" | while read -r file; do
        local size=$(du -h "$file" | cut -f1)
        local date=$(stat -c %y "$file" | cut -d' ' -f1,2 | cut -d'.' -f1)
        local basename=$(basename "$file" .sql.gz)
        
        echo "$count. $basename"
        echo "   文件: $file"
        echo "   大小: $size"
        echo "   日期: $date"
        echo ""
        
        ((count++))
    done
    
    echo "========================================"
}

# 验证备份文件
validate_backup_file() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    # 检查文件是否为空
    if [ ! -s "$backup_file" ]; then
        log_error "备份文件为空: $backup_file"
        return 1
    fi
    
    # 检查是否为gzip文件
    if ! file "$backup_file" | grep -q "gzip"; then
        log_error "备份文件不是有效的gzip文件: $backup_file"
        return 1
    fi
    
    # 测试gzip文件完整性
    if ! gzip -t "$backup_file" 2>/dev/null; then
        log_error "备份文件损坏: $backup_file"
        return 1
    fi
    
    log_success "备份文件验证通过: $backup_file"
    return 0
}

# 从备份文件恢复数据库
restore_from_backup() {
    local backup_file="$1"
    local force_restore="${2:-false}"
    
    log_info "开始从备份文件恢复: $backup_file"
    
    # 验证备份文件
    if ! validate_backup_file "$backup_file"; then
        return 1
    fi
    
    # 提取数据库名称
    local basename=$(basename "$backup_file" .sql.gz)
    local db_name=$(echo "$basename" | sed 's/_[0-9]*_[0-9]*$//')
    
    log_info "目标数据库: $db_name"
    
    # 检查数据库是否存在
    if mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $db_name;" 2>/dev/null; then
        if [ "$force_restore" != "true" ]; then
            log_warning "数据库 $db_name 已存在"
            echo -n "是否要覆盖现有数据库? (y/N): "
            read -r confirm
            if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
                log_info "恢复操作已取消"
                return 1
            fi
        fi
        log_warning "将覆盖现有数据库: $db_name"
    fi
    
    # 创建恢复前备份
    if mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $db_name;" 2>/dev/null; then
        local pre_restore_backup="/tmp/${db_name}_pre_restore_$(date +%Y%m%d_%H%M%S).sql"
        log_info "创建恢复前备份: $pre_restore_backup"
        
        mysqldump \
            -h "$MYSQL_HOST" \
            -P "$MYSQL_PORT" \
            -u "$MYSQL_USER" \
            -p"$MYSQL_PASSWORD" \
            --single-transaction \
            --databases "$db_name" > "$pre_restore_backup"
        
        gzip "$pre_restore_backup"
        log_success "恢复前备份已创建: ${pre_restore_backup}.gz"
    fi
    
    # 执行恢复
    log_info "开始恢复数据库..."
    
    if zcat "$backup_file" | mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD"; then
        log_success "数据库 $db_name 恢复完成"
        
        # 验证恢复结果
        local table_count=$(mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$db_name';" | tail -1)
        log_info "恢复后表数量: $table_count"
        
        return 0
    else
        log_error "数据库 $db_name 恢复失败"
        return 1
    fi
}

# 查找最新的备份文件
find_latest_backup() {
    local db_name="$1"
    
    if [ -z "$db_name" ]; then
        log_error "请指定数据库名称"
        return 1
    fi
    
    local latest_backup=$(find "$BACKUP_DIR" -name "${db_name}_*.sql.gz" -type f | sort -r | head -1)
    
    if [ -n "$latest_backup" ]; then
        echo "$latest_backup"
        return 0
    else
        log_error "未找到数据库 $db_name 的备份文件"
        return 1
    fi
}

# 恢复指定数据库的最新备份
restore_latest() {
    local db_name="$1"
    local force_restore="${2:-false}"
    
    log_info "查找数据库 $db_name 的最新备份..."
    
    local latest_backup=$(find_latest_backup "$db_name")
    if [ $? -ne 0 ]; then
        return 1
    fi
    
    log_info "找到最新备份: $latest_backup"
    restore_from_backup "$latest_backup" "$force_restore"
}

# 恢复所有数据库的最新备份
restore_all_latest() {
    local force_restore="${1:-false}"
    
    log_info "恢复所有数据库的最新备份..."
    
    local databases=("dl_engine_registry" "dl_engine_users" "dl_engine_projects" "dl_engine_assets" "dl_engine_render" "monitoring")
    local success_count=0
    local failed_count=0
    
    for db in "${databases[@]}"; do
        log_info "处理数据库: $db"
        
        if restore_latest "$db" "$force_restore"; then
            ((success_count++))
        else
            ((failed_count++))
            log_warning "数据库 $db 恢复失败，继续处理下一个..."
        fi
        
        echo ""
    done
    
    log_info "恢复完成 - 成功: $success_count, 失败: $failed_count"
    
    if [ $failed_count -eq 0 ]; then
        log_success "所有数据库恢复成功"
        return 0
    else
        log_error "有 $failed_count 个数据库恢复失败"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "DL引擎MySQL数据库恢复脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  list                           列出可用的备份文件"
    echo "  file <backup_file> [--force]  从指定备份文件恢复"
    echo "  latest <db_name> [--force]    恢复指定数据库的最新备份"
    echo "  all [--force]                 恢复所有数据库的最新备份"
    echo "  help                          显示帮助信息"
    echo ""
    echo "参数:"
    echo "  --force                       强制恢复，不询问确认"
    echo ""
    echo "环境变量:"
    echo "  MYSQL_HOST              MySQL主机地址 (默认: localhost)"
    echo "  MYSQL_PORT              MySQL端口 (默认: 3306)"
    echo "  MYSQL_USER              MySQL用户名 (默认: root)"
    echo "  MYSQL_ROOT_PASSWORD     MySQL密码"
    echo "  BACKUP_DIR              备份目录 (默认: /var/lib/mysql-files/backups)"
    echo ""
    echo "示例:"
    echo "  $0 list                                    # 列出备份文件"
    echo "  $0 file /path/to/backup.sql.gz            # 从文件恢复"
    echo "  $0 latest monitoring                      # 恢复monitoring数据库"
    echo "  $0 latest monitoring --force              # 强制恢复"
    echo "  $0 all                                    # 恢复所有数据库"
}

# 主函数
main() {
    local action="$1"
    
    case "$action" in
        "list")
            list_backups
            ;;
        "file")
            local backup_file="$2"
            local force_restore="false"
            
            if [ "$3" = "--force" ]; then
                force_restore="true"
            fi
            
            if [ -z "$backup_file" ]; then
                log_error "请指定备份文件路径"
                show_help
                exit 1
            fi
            
            check_mysql_connection || exit 1
            restore_from_backup "$backup_file" "$force_restore"
            ;;
        "latest")
            local db_name="$2"
            local force_restore="false"
            
            if [ "$3" = "--force" ]; then
                force_restore="true"
            fi
            
            if [ -z "$db_name" ]; then
                log_error "请指定数据库名称"
                show_help
                exit 1
            fi
            
            check_mysql_connection || exit 1
            restore_latest "$db_name" "$force_restore"
            ;;
        "all")
            local force_restore="false"
            
            if [ "$2" = "--force" ]; then
                force_restore="true"
            fi
            
            check_mysql_connection || exit 1
            restore_all_latest "$force_restore"
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
