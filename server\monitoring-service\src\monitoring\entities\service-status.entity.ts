import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum ServiceStatus {
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  DEGRADED = 'degraded',
  UNKNOWN = 'unknown',
}

@Entity('service_status')
@Index(['serviceName', 'updatedAt'])
export class ServiceStatusEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100, unique: true })
  serviceName: string;

  @Column({ length: 200, nullable: true })
  serviceUrl: string;

  @Column({
    type: 'enum',
    enum: ServiceStatus,
    default: ServiceStatus.UNKNOWN,
  })
  status: ServiceStatus;

  @Column({ type: 'int', nullable: true })
  responseTime: number;

  @Column({ type: 'text', nullable: true })
  lastError: string;

  @Column({ type: 'int', default: 0 })
  consecutiveFailures: number;

  @Column({ type: 'timestamp', nullable: true })
  lastCheckAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastHealthyAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
