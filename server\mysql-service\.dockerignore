# Docker ignore file for MySQL service

# 环境变量文件
.env
.env.local
.env.*.local

# 日志文件
*.log
logs/

# 数据目录
data/
backups/

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# 版本控制
.git
.gitignore

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 文档文件
README.md
*.md

# Docker相关
docker-compose*.yml
Dockerfile.*

# 测试文件
test/
tests/
*.test

# 备份文件
*.bak
*.backup

# 压缩文件
*.tar
*.tar.gz
*.zip

# 系统文件
.DS_Store
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
