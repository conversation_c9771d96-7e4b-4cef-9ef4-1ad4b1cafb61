# DL引擎Redis配置文件
# 针对微服务架构优化的Redis配置

# ================================
# 网络配置
# ================================

# 绑定地址（允许所有IP连接）
bind 0.0.0.0

# 端口配置
port 6379

# 保护模式（在Docker环境中禁用）
protected-mode no

# TCP监听队列长度
tcp-backlog 511

# 客户端超时时间（0表示禁用）
timeout 0

# TCP keepalive
tcp-keepalive 300

# ================================
# 通用配置
# ================================

# 守护进程模式（Docker中设为no）
daemonize no

# 进程文件
pidfile /var/run/redis.pid

# 日志级别：debug, verbose, notice, warning
loglevel notice

# 日志文件
logfile /var/log/redis/redis.log

# 数据库数量（支持16个逻辑数据库）
databases 16

# 总是显示logo
always-show-logo yes

# ================================
# 快照配置（RDB持久化）
# ================================

# 自动保存条件
# 900秒内至少1个key发生变化
save 900 1
# 300秒内至少10个key发生变化
save 300 10
# 60秒内至少10000个key发生变化
save 60 10000

# RDB文件压缩
rdbcompression yes

# RDB文件校验
rdbchecksum yes

# RDB文件名
dbfilename dump.rdb

# 数据目录
dir /var/lib/redis

# ================================
# AOF持久化配置
# ================================

# 启用AOF持久化
appendonly yes

# AOF文件名
appendfilename "appendonly.aof"

# AOF同步策略
# always: 每个写命令都同步
# everysec: 每秒同步一次（推荐）
# no: 由操作系统决定
appendfsync everysec

# 重写时不进行AOF同步
no-appendfsync-on-rewrite no

# AOF重写触发条件
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# AOF加载时忽略最后的不完整命令
aof-load-truncated yes

# 混合持久化（RDB+AOF）
aof-use-rdb-preamble yes

# ================================
# 内存管理
# ================================

# 最大内存限制（根据容器内存调整）
# maxmemory 1gb

# 内存淘汰策略
# allkeys-lru: 所有key中删除最近最少使用的
# volatile-lru: 过期key中删除最近最少使用的
# allkeys-random: 所有key中随机删除
# volatile-random: 过期key中随机删除
# volatile-ttl: 删除即将过期的key
# noeviction: 不删除，返回错误
maxmemory-policy allkeys-lru

# 内存采样数量
maxmemory-samples 5

# ================================
# 连接配置
# ================================

# 最大客户端连接数
maxclients 10000

# ================================
# 安全配置
# ================================

# 密码认证（生产环境建议设置）
# requirepass your_password_here

# 重命名危险命令
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG "CONFIG_DL_ENGINE"

# ================================
# 慢查询日志
# ================================

# 慢查询阈值（微秒）
slowlog-log-slower-than 10000

# 慢查询日志长度
slowlog-max-len 128

# ================================
# 延迟监控
# ================================

# 延迟监控阈值（毫秒）
latency-monitor-threshold 100

# ================================
# 事件通知
# ================================

# 键空间通知
# K: 键空间事件
# E: 键事件
# g: DEL, EXPIRE, RENAME等通用命令
# $: 字符串命令
# l: 列表命令
# s: 集合命令
# h: 哈希命令
# z: 有序集合命令
# x: 过期事件
# e: 驱逐事件
notify-keyspace-events "Ex"

# ================================
# 高级配置
# ================================

# 哈希表配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog配置
hll-sparse-max-bytes 3000

# 流配置
stream-node-max-bytes 4096
stream-node-max-entries 100

# ================================
# 活跃重新哈希
# ================================

# 每100毫秒花费1毫秒进行主动过期检查
activerehashing yes

# ================================
# 客户端输出缓冲区限制
# ================================

# 普通客户端
client-output-buffer-limit normal 0 0 0

# 副本客户端
client-output-buffer-limit replica 256mb 64mb 60

# 发布订阅客户端
client-output-buffer-limit pubsub 32mb 8mb 60

# ================================
# 客户端查询缓冲区限制
# ================================

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议缓冲区限制
proto-max-bulk-len 512mb

# ================================
# 频率限制
# ================================

# 后台任务频率
hz 10

# 动态频率调整
dynamic-hz yes

# ================================
# AOF重写配置
# ================================

# AOF重写时的缓冲区大小
aof-rewrite-incremental-fsync yes

# ================================
# RDB配置
# ================================

# RDB保存失败时停止写入
stop-writes-on-bgsave-error yes

# ================================
# 副本配置
# ================================

# 副本只读
replica-read-only yes

# 副本优先级
replica-priority 100

# ================================
# 模块配置
# ================================

# 加载模块（如果需要）
# loadmodule /path/to/module.so

# ================================
# 包含其他配置文件
# ================================

# include /path/to/other.conf
