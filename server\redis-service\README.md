# DL引擎Redis缓存服务

专为DL（Digital Learning）引擎微服务架构设计的Redis缓存服务，提供统一的缓存、消息队列和会话存储解决方案。

## 🏗️ 架构特性

### 数据库分配
- **DB 0**: 服务注册中心缓存
- **DB 1**: 用户服务缓存和会话
- **DB 2**: 项目服务缓存
- **DB 3**: 资产服务缓存
- **DB 4**: 渲染服务任务队列
- **DB 5**: 协作服务实时数据
- **DB 6**: 监控服务缓存
- **DB 7**: API网关缓存和限流
- **DB 8**: 事件总线消息
- **DB 9**: 分布式锁
- **DB 10**: 临时数据
- **DB 11-15**: 预留扩展

### 核心功能
- ✅ **多级缓存**: 支持16个逻辑数据库
- ✅ **任务队列**: Bull队列支持（渲染服务）
- ✅ **事件总线**: 微服务间消息传递
- ✅ **会话存储**: 用户会话管理
- ✅ **分布式锁**: 服务协调机制
- ✅ **持久化**: RDB + AOF双重保障
- ✅ **监控告警**: 完整的性能监控
- ✅ **自动备份**: 定时备份和恢复
- ✅ **管理界面**: Redis Commander支持

## 🚀 快速开始

### 1. 环境准备
```bash
# 进入Redis服务目录
cd server/redis-service

# 复制环境变量配置
cp .env.example .env

# 编辑配置文件
vim .env
```

### 2. 启动服务
```bash
# 启动Redis服务
docker-compose up -d dl-engine-redis

# 查看启动日志
docker-compose logs -f dl-engine-redis

# 检查服务状态
docker-compose ps
```

### 3. 验证安装
```bash
# 健康检查
docker exec dl-engine-redis /usr/local/bin/health-check.sh

# 连接测试
redis-cli -h localhost -p 6379 ping

# 查看数据库分配
docker exec dl-engine-redis cat /var/lib/redis/database_allocation.txt
```

## 📊 使用场景

### 缓存服务
```bash
# 服务注册中心缓存 (DB 0)
redis-cli -n 0 set "service:user-service" "http://user-service:4001"

# 用户会话存储 (DB 1)
redis-cli -n 1 setex "session:user123" 3600 "user_data"

# 项目缓存 (DB 2)
redis-cli -n 2 hset "project:123" "name" "My Project"
```

### 任务队列
```bash
# 渲染任务队列 (DB 4)
redis-cli -n 4 lpush "queue:render" "task_data"

# 获取任务
redis-cli -n 4 brpop "queue:render" 0
```

### 分布式锁
```bash
# 获取分布式锁 (DB 9)
redis-cli -n 9 set "lock:resource" "owner" EX 30 NX

# 释放锁
redis-cli -n 9 del "lock:resource"
```

## 🔧 配置说明

### 核心配置
```bash
# Redis基本配置
REDIS_PASSWORD=your_secure_password
REDIS_PORT=6379
REDIS_MAX_MEMORY=1gb

# 持久化配置
REDIS_ENABLE_AOF=true
REDIS_ENABLE_RDB=true

# 监控配置
MONITOR_INTERVAL=30
```

### 性能优化
```bash
# 内存管理
MAXMEMORY_POLICY=allkeys-lru
MAX_CLIENTS=10000

# 慢查询监控
SLOWLOG_SLOWER_THAN=10000
SLOWLOG_MAX_LEN=128
```

## 🛠️ 管理工具

### 健康检查
```bash
# 完整健康检查
docker exec dl-engine-redis /usr/local/bin/health-check.sh full

# 简单连接检查
docker exec dl-engine-redis /usr/local/bin/health-check.sh simple
```

### 性能监控
```bash
# 启动监控
docker exec dl-engine-redis /usr/local/bin/monitor.sh monitor

# 一次性监控
docker exec dl-engine-redis /usr/local/bin/monitor.sh once
```

### 数据备份
```bash
# 完整备份
docker exec dl-engine-redis /usr/local/bin/backup.sh full

# RDB备份
docker exec dl-engine-redis /usr/local/bin/backup.sh rdb

# AOF备份
docker exec dl-engine-redis /usr/local/bin/backup.sh aof

# 查看备份状态
docker exec dl-engine-redis /usr/local/bin/backup.sh status
```

### 数据恢复
```bash
# 列出备份文件
docker exec dl-engine-redis /usr/local/bin/restore.sh list

# 从最新备份恢复
docker exec dl-engine-redis /usr/local/bin/restore.sh latest

# 从指定文件恢复
docker exec dl-engine-redis /usr/local/bin/restore.sh file /path/to/backup.rdb.gz
```

## 📈 监控指标

### 系统指标
- 内存使用率和碎片率
- 客户端连接数
- 命令执行频率
- 网络IO统计
- 键空间命中率

### 业务指标
- 各数据库键数量
- 过期键统计
- 慢查询数量
- 持久化状态

### 告警规则
- 内存使用率 > 90%
- 内存碎片率 > 2.0
- 客户端连接数 > 1000
- 缓存命中率 < 80%
- 慢查询数量异常

## 🐳 Docker配置

### 服务组件
```yaml
services:
  dl-engine-redis:        # 主Redis服务
  redis-commander:        # Web管理界面
  redis-monitor:          # 性能监控服务
  redis-backup:           # 定时备份服务
  redis-exporter:         # Prometheus监控导出器
```

### 数据卷
```yaml
volumes:
  redis_data:             # 数据持久化
  redis_logs:             # 日志存储
  redis_backups:          # 备份存储
```

### 资源限制
- 内存限制: 1GB
- CPU限制: 0.5核心
- 内存预留: 512MB
- CPU预留: 0.25核心

## 🔄 维护操作

### 日常维护
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs dl-engine-redis

# 重启服务
docker-compose restart dl-engine-redis

# 查看Redis信息
docker exec dl-engine-redis redis-cli info
```

### 性能调优
```bash
# 查看慢查询
docker exec dl-engine-redis redis-cli slowlog get 10

# 查看客户端连接
docker exec dl-engine-redis redis-cli client list

# 内存分析
docker exec dl-engine-redis redis-cli memory usage key_name
```

## 🔒 安全配置

### 访问控制
- 密码认证（可选）
- 危险命令重命名
- 网络访问限制
- 容器安全配置

### 数据安全
- RDB + AOF双重持久化
- 定时备份机制
- 数据恢复验证
- 权限最小化原则

## 🚨 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 检查端口
   netstat -tulpn | grep 6379
   
   # 检查日志
   docker-compose logs dl-engine-redis
   ```

2. **内存不足**
   ```bash
   # 检查内存使用
   docker exec dl-engine-redis redis-cli info memory
   
   # 清理过期键
   docker exec dl-engine-redis redis-cli flushdb
   
   # 调整内存限制
   vim .env  # 修改REDIS_MAX_MEMORY
   ```

3. **性能问题**
   ```bash
   # 检查慢查询
   docker exec dl-engine-redis redis-cli slowlog get
   
   # 检查客户端连接
   docker exec dl-engine-redis redis-cli client list
   
   # 优化配置
   vim config/redis.conf
   ```

## 📝 版本信息

- **Redis版本**: 7.0
- **服务版本**: 1.0.0
- **支持架构**: x86_64, ARM64
- **最低内存**: 512MB
- **推荐内存**: 1GB+

## 🤝 技术支持

如遇到问题，请按以下步骤排查：

1. 检查环境变量配置
2. 查看容器日志
3. 执行健康检查
4. 验证网络连接
5. 检查资源使用情况

更多详细信息请参考DL引擎官方文档。
