# DL引擎Redis服务网络配置指南

## 🌐 网络问题解决方案

### 问题描述
构建Redis镜像时出现网络连接错误：
```
ERROR: failed to solve: redis:7.0-alpine: failed to resolve source metadata for docker.io/library/redis:7.0-alpine
```

这通常是由于以下原因造成的：
1. 网络连接不稳定
2. Docker Hub访问受限
3. DNS解析问题
4. 防火墙或代理设置

## 🔧 解决方案

### 方案一：配置Docker镜像加速器（推荐）

#### Windows Docker Desktop
1. 打开Docker Desktop
2. 点击设置图标 → Settings
3. 选择 Docker Engine
4. 在JSON配置中添加：

```json
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://dockerhub.azk8s.cn"
  ]
}
```

5. 点击 "Apply & Restart"

#### Linux系统
创建或编辑 `/etc/docker/daemon.json`：

```bash
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://dockerhub.azk8s.cn"
  ]
}
EOF

sudo systemctl daemon-reload
sudo systemctl restart docker
```

#### macOS Docker Desktop
1. 打开Docker Desktop
2. 点击齿轮图标 → Preferences
3. 选择 Docker Engine
4. 添加镜像加速器配置
5. 点击 "Apply & Restart"

### 方案二：使用国内镜像加速器

#### 阿里云镜像加速器
1. 访问：https://cr.console.aliyun.com/cn-hangzhou/instances/mirrors
2. 登录阿里云账号
3. 获取专属加速器地址
4. 按照页面指引配置

#### 腾讯云镜像加速器
1. 访问：https://cloud.tencent.com/document/product/1141/50332
2. 获取加速器地址：`https://mirror.ccs.tencentyun.com`

#### 网易云镜像加速器
- 地址：`http://hub-mirror.c.163.com`

### 方案三：使用代理服务器

#### 构建时使用代理
```bash
docker build \
  --build-arg HTTP_PROXY=http://proxy.company.com:8080 \
  --build-arg HTTPS_PROXY=http://proxy.company.com:8080 \
  -t dl-engine-redis .
```

#### 配置Docker守护进程代理
创建 `/etc/systemd/system/docker.service.d/http-proxy.conf`：

```ini
[Service]
Environment="HTTP_PROXY=http://proxy.company.com:8080"
Environment="HTTPS_PROXY=http://proxy.company.com:8080"
Environment="NO_PROXY=localhost,127.0.0.1"
```

### 方案四：离线构建

#### 使用离线Dockerfile
```bash
# 使用离线构建版本
docker build -f Dockerfile.offline -t dl-engine-redis .
```

#### 预先下载镜像
```bash
# 在网络良好时预先下载
docker pull redis:alpine
docker pull alpine:latest

# 保存镜像到文件
docker save redis:alpine > redis-alpine.tar
docker save alpine:latest > alpine-latest.tar

# 在离线环境加载镜像
docker load < redis-alpine.tar
docker load < alpine-latest.tar
```

### 方案五：修改基础镜像

#### 使用更通用的标签
```dockerfile
# 原始版本
FROM redis:7.0-alpine

# 修改为更通用的版本
FROM redis:alpine
# 或
FROM redis:latest
```

## 🛠️ 网络诊断工具

### 使用网络诊断脚本
```bash
# Linux/Mac
./scripts/network-check.sh

# Windows (Git Bash)
bash scripts/network-check.sh
```

### 手动网络测试
```bash
# 测试DNS解析
nslookup docker.io

# 测试连接
curl -I https://docker.io

# 测试镜像拉取
docker pull hello-world

# 查看Docker配置
docker info
```

## 📋 构建步骤

### 推荐构建流程

1. **配置镜像加速器**
   ```bash
   # 按照上述方案配置加速器
   ```

2. **验证网络连接**
   ```bash
   ./scripts/network-check.sh
   ```

3. **构建镜像**
   ```bash
   # Windows
   build.bat
   
   # Linux/Mac
   ./build.sh
   ```

4. **如果构建失败，使用离线模式**
   ```bash
   docker build -f Dockerfile.offline -t dl-engine-redis .
   ```

### 企业环境建议

1. **内部镜像仓库**
   - 搭建Harbor或其他私有仓库
   - 同步常用镜像到内部仓库

2. **网络策略**
   - 配置企业代理服务器
   - 开放Docker Hub相关域名

3. **离线部署**
   - 预先下载所需镜像
   - 使用镜像打包分发

## 🔍 故障排除

### 常见错误及解决方案

#### 错误1：连接超时
```
dial tcp: i/o timeout
```
**解决方案：**
- 配置镜像加速器
- 检查网络连接
- 使用代理服务器

#### 错误2：DNS解析失败
```
no such host
```
**解决方案：**
- 检查DNS设置
- 使用公共DNS（*******）
- 配置hosts文件

#### 错误3：认证失败
```
failed to authorize
```
**解决方案：**
- 重启Docker服务
- 清理Docker缓存
- 使用不同的镜像源

### 验证配置
```bash
# 检查镜像加速器配置
docker info | grep -A 10 "Registry Mirrors"

# 测试镜像拉取速度
time docker pull hello-world

# 查看Docker版本
docker --version
```

## 📞 技术支持

如果仍然遇到网络问题：

1. 检查企业网络策略
2. 联系网络管理员
3. 考虑使用离线部署方案
4. 参考Docker官方文档

更多信息请参考：
- [Docker官方镜像加速器文档](https://docs.docker.com/registry/recipes/mirror/)
- [阿里云镜像加速器](https://cr.console.aliyun.com/cn-hangzhou/instances/mirrors)
- [腾讯云镜像加速器](https://cloud.tencent.com/document/product/1141/50332)
