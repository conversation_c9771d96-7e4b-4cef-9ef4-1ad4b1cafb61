#!/bin/bash

# DL引擎MySQL数据库备份脚本
# 支持全量备份、增量备份和指定数据库备份

set -e

# 配置变量
MYSQL_HOST=${MYSQL_HOST:-"localhost"}
MYSQL_PORT=${MYSQL_PORT:-"3306"}
MYSQL_USER=${MYSQL_USER:-"root"}
MYSQL_PASSWORD=${MYSQL_ROOT_PASSWORD:-"dl_engine_password_2024"}
BACKUP_DIR=${BACKUP_DIR:-"/var/lib/mysql-files/backups"}
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-"7"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 创建备份目录
create_backup_dir() {
    local backup_date=$(date '+%Y-%m-%d')
    local backup_path="$BACKUP_DIR/$backup_date"
    
    if [ ! -d "$backup_path" ]; then
        mkdir -p "$backup_path"
        log_info "创建备份目录: $backup_path"
    fi
    
    echo "$backup_path"
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    
    if mysqladmin ping -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" --silent 2>/dev/null; then
        log_success "MySQL连接正常"
        return 0
    else
        log_error "MySQL连接失败"
        return 1
    fi
}

# 获取数据库列表
get_databases() {
    local databases=$(mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SHOW DATABASES;" | grep -E "(dl_engine_|monitoring)" | grep -v "information_schema\|performance_schema\|mysql\|sys")
    echo "$databases"
}

# 备份单个数据库
backup_database() {
    local db_name="$1"
    local backup_path="$2"
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$backup_path/${db_name}_${timestamp}.sql"
    
    log_info "开始备份数据库: $db_name"
    
    # 执行备份
    if mysqldump \
        -h "$MYSQL_HOST" \
        -P "$MYSQL_PORT" \
        -u "$MYSQL_USER" \
        -p"$MYSQL_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        --add-drop-database \
        --databases "$db_name" > "$backup_file"; then
        
        # 压缩备份文件
        gzip "$backup_file"
        backup_file="${backup_file}.gz"
        
        # 获取文件大小
        local file_size=$(du -h "$backup_file" | cut -f1)
        
        log_success "数据库 $db_name 备份完成: $backup_file ($file_size)"
        
        # 验证备份文件
        if [ -f "$backup_file" ] && [ -s "$backup_file" ]; then
            log_success "备份文件验证通过"
            return 0
        else
            log_error "备份文件验证失败"
            return 1
        fi
    else
        log_error "数据库 $db_name 备份失败"
        return 1
    fi
}

# 全量备份
full_backup() {
    log_info "开始执行全量备份..."
    
    local backup_path=$(create_backup_dir)
    local databases=$(get_databases)
    local failed_count=0
    local success_count=0
    
    if [ -z "$databases" ]; then
        log_warning "未找到需要备份的数据库"
        return 1
    fi
    
    log_info "发现数据库: $(echo $databases | tr '\n' ' ')"
    
    # 备份每个数据库
    for db in $databases; do
        if backup_database "$db" "$backup_path"; then
            ((success_count++))
        else
            ((failed_count++))
        fi
    done
    
    # 创建备份清单
    local manifest_file="$backup_path/backup_manifest.txt"
    {
        echo "DL引擎数据库备份清单"
        echo "备份时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "备份类型: 全量备份"
        echo "MySQL版本: $(mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT VERSION();" | tail -1)"
        echo "成功备份: $success_count 个数据库"
        echo "失败备份: $failed_count 个数据库"
        echo ""
        echo "备份文件列表:"
        ls -lh "$backup_path"/*.sql.gz 2>/dev/null || echo "无备份文件"
    } > "$manifest_file"
    
    log_info "备份清单已创建: $manifest_file"
    
    if [ $failed_count -eq 0 ]; then
        log_success "全量备份完成，共备份 $success_count 个数据库"
        return 0
    else
        log_error "全量备份完成，但有 $failed_count 个数据库备份失败"
        return 1
    fi
}

# 备份指定数据库
backup_specific_database() {
    local db_name="$1"
    
    if [ -z "$db_name" ]; then
        log_error "请指定要备份的数据库名称"
        return 1
    fi
    
    log_info "开始备份指定数据库: $db_name"
    
    # 检查数据库是否存在
    if ! mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $db_name;" 2>/dev/null; then
        log_error "数据库 $db_name 不存在"
        return 1
    fi
    
    local backup_path=$(create_backup_dir)
    
    if backup_database "$db_name" "$backup_path"; then
        log_success "数据库 $db_name 备份完成"
        return 0
    else
        log_error "数据库 $db_name 备份失败"
        return 1
    fi
}

# 清理过期备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的备份文件..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "备份目录不存在: $BACKUP_DIR"
        return 0
    fi
    
    local deleted_count=0
    
    # 查找并删除过期的备份目录
    find "$BACKUP_DIR" -type d -name "20*" -mtime +$RETENTION_DAYS | while read -r old_dir; do
        if [ -d "$old_dir" ]; then
            log_info "删除过期备份目录: $old_dir"
            rm -rf "$old_dir"
            ((deleted_count++))
        fi
    done
    
    if [ $deleted_count -gt 0 ]; then
        log_success "清理完成，删除了 $deleted_count 个过期备份"
    else
        log_info "没有需要清理的过期备份"
    fi
}

# 显示备份状态
show_backup_status() {
    log_info "备份状态信息:"
    echo "========================================"
    
    if [ -d "$BACKUP_DIR" ]; then
        echo "备份目录: $BACKUP_DIR"
        echo "目录大小: $(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1)"
        echo ""
        
        echo "最近的备份:"
        find "$BACKUP_DIR" -name "*.sql.gz" -type f -printf "%T@ %Tc %p\n" 2>/dev/null | sort -n | tail -10 | while read -r timestamp date_str file; do
            local size=$(du -h "$file" | cut -f1)
            echo "  $(basename "$file") - $size - $date_str"
        done
    else
        echo "备份目录不存在: $BACKUP_DIR"
    fi
    
    echo "========================================"
}

# 显示帮助信息
show_help() {
    echo "DL引擎MySQL数据库备份脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  full                    执行全量备份（默认）"
    echo "  database <db_name>      备份指定数据库"
    echo "  cleanup                 清理过期备份"
    echo "  status                  显示备份状态"
    echo "  help                    显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  MYSQL_HOST              MySQL主机地址 (默认: localhost)"
    echo "  MYSQL_PORT              MySQL端口 (默认: 3306)"
    echo "  MYSQL_USER              MySQL用户名 (默认: root)"
    echo "  MYSQL_ROOT_PASSWORD     MySQL密码"
    echo "  BACKUP_DIR              备份目录 (默认: /var/lib/mysql-files/backups)"
    echo "  BACKUP_RETENTION_DAYS   备份保留天数 (默认: 7)"
    echo ""
    echo "示例:"
    echo "  $0                      # 执行全量备份"
    echo "  $0 database monitoring  # 备份monitoring数据库"
    echo "  $0 cleanup              # 清理过期备份"
    echo "  $0 status               # 查看备份状态"
}

# 主函数
main() {
    local action="${1:-full}"
    
    case "$action" in
        "full")
            check_mysql_connection || exit 1
            full_backup
            ;;
        "database")
            local db_name="$2"
            check_mysql_connection || exit 1
            backup_specific_database "$db_name"
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "status")
            show_backup_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
