#!/bin/bash

# DL引擎Redis数据备份脚本
# 支持RDB和AOF备份

set -e

# 配置变量
REDIS_HOST=${REDIS_HOST:-"127.0.0.1"}
REDIS_PORT=${REDIS_PORT:-"6379"}
REDIS_PASSWORD=${REDIS_PASSWORD:-""}
BACKUP_DIR=${BACKUP_DIR:-"/var/lib/redis/backups"}
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-"7"}
REDIS_DATA_DIR=${REDIS_DATA_DIR:-"/var/lib/redis"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 构建Redis连接命令
build_redis_cmd() {
    local cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
    
    if [ -n "$REDIS_PASSWORD" ]; then
        cmd="$cmd -a $REDIS_PASSWORD"
    fi
    
    echo "$cmd"
}

# 创建备份目录
create_backup_dir() {
    local backup_date=$(date '+%Y-%m-%d')
    local backup_path="$BACKUP_DIR/$backup_date"
    
    if [ ! -d "$backup_path" ]; then
        mkdir -p "$backup_path"
        log_info "创建备份目录: $backup_path"
    fi
    
    echo "$backup_path"
}

# 检查Redis连接
check_redis_connection() {
    log_info "检查Redis连接..."
    
    local redis_cmd=$(build_redis_cmd)
    
    if $redis_cmd ping >/dev/null 2>&1; then
        log_success "Redis连接正常"
        return 0
    else
        log_error "Redis连接失败"
        return 1
    fi
}

# RDB备份
backup_rdb() {
    log_info "开始RDB备份..."
    
    local redis_cmd=$(build_redis_cmd)
    local backup_path=$(create_backup_dir)
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$backup_path/redis_rdb_${timestamp}.rdb"
    
    # 触发RDB保存
    log_info "触发RDB保存..."
    if $redis_cmd bgsave >/dev/null 2>&1; then
        log_success "RDB保存命令执行成功"
        
        # 等待备份完成
        log_info "等待RDB备份完成..."
        while true; do
            local last_save=$($redis_cmd lastsave 2>/dev/null)
            sleep 2
            local current_save=$($redis_cmd lastsave 2>/dev/null)
            
            if [ "$current_save" != "$last_save" ]; then
                log_success "RDB备份完成"
                break
            fi
            
            # 检查是否有错误
            local bgsave_status=$($redis_cmd info persistence | grep "rdb_last_bgsave_status" | cut -d: -f2 | tr -d '\r')
            if [ "$bgsave_status" = "err" ]; then
                log_error "RDB备份失败"
                return 1
            fi
            
            log_info "等待RDB备份完成..."
        done
        
        # 复制RDB文件
        local rdb_file="$REDIS_DATA_DIR/dump.rdb"
        if [ -f "$rdb_file" ]; then
            cp "$rdb_file" "$backup_file"
            
            # 压缩备份文件
            gzip "$backup_file"
            backup_file="${backup_file}.gz"
            
            # 获取文件大小
            local file_size=$(du -h "$backup_file" | cut -f1)
            
            log_success "RDB备份完成: $backup_file ($file_size)"
            return 0
        else
            log_error "RDB文件不存在: $rdb_file"
            return 1
        fi
    else
        log_error "RDB保存命令执行失败"
        return 1
    fi
}

# AOF备份
backup_aof() {
    log_info "开始AOF备份..."
    
    local redis_cmd=$(build_redis_cmd)
    local backup_path=$(create_backup_dir)
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$backup_path/redis_aof_${timestamp}.aof"
    
    # 检查AOF是否启用
    local aof_enabled=$($redis_cmd config get appendonly | tail -1)
    if [ "$aof_enabled" != "yes" ]; then
        log_warning "AOF未启用，跳过AOF备份"
        return 0
    fi
    
    # 触发AOF重写
    log_info "触发AOF重写..."
    if $redis_cmd bgrewriteaof >/dev/null 2>&1; then
        log_success "AOF重写命令执行成功"
        
        # 等待AOF重写完成
        log_info "等待AOF重写完成..."
        while true; do
            local aof_rewrite_in_progress=$($redis_cmd info persistence | grep "aof_rewrite_in_progress" | cut -d: -f2 | tr -d '\r')
            
            if [ "$aof_rewrite_in_progress" = "0" ]; then
                log_success "AOF重写完成"
                break
            fi
            
            log_info "等待AOF重写完成..."
            sleep 2
        done
        
        # 复制AOF文件
        local aof_file="$REDIS_DATA_DIR/appendonly.aof"
        if [ -f "$aof_file" ]; then
            cp "$aof_file" "$backup_file"
            
            # 压缩备份文件
            gzip "$backup_file"
            backup_file="${backup_file}.gz"
            
            # 获取文件大小
            local file_size=$(du -h "$backup_file" | cut -f1)
            
            log_success "AOF备份完成: $backup_file ($file_size)"
            return 0
        else
            log_error "AOF文件不存在: $aof_file"
            return 1
        fi
    else
        log_error "AOF重写命令执行失败"
        return 1
    fi
}

# 完整备份
full_backup() {
    log_info "开始完整备份..."
    
    local backup_path=$(create_backup_dir)
    local failed_count=0
    local success_count=0
    
    # RDB备份
    if backup_rdb; then
        ((success_count++))
    else
        ((failed_count++))
    fi
    
    # AOF备份
    if backup_aof; then
        ((success_count++))
    else
        ((failed_count++))
    fi
    
    # 备份配置信息
    backup_config "$backup_path"
    
    # 创建备份清单
    create_backup_manifest "$backup_path" "$success_count" "$failed_count"
    
    if [ $failed_count -eq 0 ]; then
        log_success "完整备份完成，共备份 $success_count 个文件"
        return 0
    else
        log_error "完整备份完成，但有 $failed_count 个备份失败"
        return 1
    fi
}

# 备份配置信息
backup_config() {
    local backup_path="$1"
    local redis_cmd=$(build_redis_cmd)
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local config_file="$backup_path/redis_config_${timestamp}.txt"
    
    log_info "备份Redis配置信息..."
    
    {
        echo "Redis配置信息备份"
        echo "备份时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "Redis版本: $($redis_cmd info server | grep "redis_version" | cut -d: -f2 | tr -d '\r')"
        echo ""
        echo "=== 服务器信息 ==="
        $redis_cmd info server
        echo ""
        echo "=== 内存信息 ==="
        $redis_cmd info memory
        echo ""
        echo "=== 持久化信息 ==="
        $redis_cmd info persistence
        echo ""
        echo "=== 配置信息 ==="
        $redis_cmd config get "*"
    } > "$config_file" 2>/dev/null
    
    if [ -f "$config_file" ]; then
        gzip "$config_file"
        log_success "配置信息备份完成: ${config_file}.gz"
    else
        log_warning "配置信息备份失败"
    fi
}

# 创建备份清单
create_backup_manifest() {
    local backup_path="$1"
    local success_count="$2"
    local failed_count="$3"
    local manifest_file="$backup_path/backup_manifest.txt"
    
    log_info "创建备份清单: $manifest_file"
    
    {
        echo "DL引擎Redis备份清单"
        echo "备份时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "备份类型: 完整备份"
        echo "Redis主机: $REDIS_HOST:$REDIS_PORT"
        echo "成功备份: $success_count 个文件"
        echo "失败备份: $failed_count 个文件"
        echo ""
        echo "备份文件列表:"
        ls -lh "$backup_path"/*.gz 2>/dev/null || echo "无备份文件"
    } > "$manifest_file"
    
    log_success "备份清单已创建: $manifest_file"
}

# 清理过期备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的备份文件..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "备份目录不存在: $BACKUP_DIR"
        return 0
    fi
    
    local deleted_count=0
    
    # 查找并删除过期的备份目录
    find "$BACKUP_DIR" -type d -name "20*" -mtime +$RETENTION_DAYS | while read -r old_dir; do
        if [ -d "$old_dir" ]; then
            log_info "删除过期备份目录: $old_dir"
            rm -rf "$old_dir"
            ((deleted_count++))
        fi
    done
    
    if [ $deleted_count -gt 0 ]; then
        log_success "清理完成，删除了 $deleted_count 个过期备份"
    else
        log_info "没有需要清理的过期备份"
    fi
}

# 显示备份状态
show_backup_status() {
    log_info "备份状态信息:"
    echo "========================================"
    
    if [ -d "$BACKUP_DIR" ]; then
        echo "备份目录: $BACKUP_DIR"
        echo "目录大小: $(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1)"
        echo ""
        
        echo "最近的备份:"
        find "$BACKUP_DIR" -name "*.gz" -type f -printf "%T@ %Tc %p\n" 2>/dev/null | sort -n | tail -10 | while read -r timestamp date_str file; do
            local size=$(du -h "$file" | cut -f1)
            echo "  $(basename "$file") - $size - $date_str"
        done
    else
        echo "备份目录不存在: $BACKUP_DIR"
    fi
    
    echo "========================================"
}

# 显示帮助信息
show_help() {
    echo "DL引擎Redis数据备份脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  full                    执行完整备份（默认）"
    echo "  rdb                     仅RDB备份"
    echo "  aof                     仅AOF备份"
    echo "  cleanup                 清理过期备份"
    echo "  status                  显示备份状态"
    echo "  help                    显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  REDIS_HOST              Redis主机地址 (默认: 127.0.0.1)"
    echo "  REDIS_PORT              Redis端口 (默认: 6379)"
    echo "  REDIS_PASSWORD          Redis密码"
    echo "  BACKUP_DIR              备份目录 (默认: /var/lib/redis/backups)"
    echo "  BACKUP_RETENTION_DAYS   备份保留天数 (默认: 7)"
    echo "  REDIS_DATA_DIR          Redis数据目录 (默认: /var/lib/redis)"
    echo ""
    echo "示例:"
    echo "  $0                      # 执行完整备份"
    echo "  $0 rdb                  # 仅RDB备份"
    echo "  $0 cleanup              # 清理过期备份"
    echo "  $0 status               # 查看备份状态"
}

# 主函数
main() {
    local action="${1:-full}"
    
    case "$action" in
        "full")
            check_redis_connection || exit 1
            full_backup
            ;;
        "rdb")
            check_redis_connection || exit 1
            backup_rdb
            ;;
        "aof")
            check_redis_connection || exit 1
            backup_aof
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "status")
            show_backup_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
