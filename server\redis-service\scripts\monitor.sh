#!/bin/bash

# DL引擎Redis监控脚本
# 持续监控Redis性能指标和状态

set -e

# 配置变量
REDIS_HOST=${REDIS_HOST:-"127.0.0.1"}
REDIS_PORT=${REDIS_PORT:-"6379"}
REDIS_PASSWORD=${REDIS_PASSWORD:-""}
MONITOR_INTERVAL=${MONITOR_INTERVAL:-"30"}
LOG_FILE=${MONITOR_LOG_FILE:-"/var/log/redis/monitor.log"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1" | tee -a "$LOG_FILE"
}

# 构建Redis连接命令
build_redis_cmd() {
    local cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
    
    if [ -n "$REDIS_PASSWORD" ]; then
        cmd="$cmd -a $REDIS_PASSWORD"
    fi
    
    echo "$cmd"
}

# 获取Redis信息
get_redis_info() {
    local section="$1"
    local redis_cmd=$(build_redis_cmd)
    
    $redis_cmd info "$section" 2>/dev/null
}

# 监控内存使用
monitor_memory() {
    local memory_info=$(get_redis_info memory)
    
    if [ -n "$memory_info" ]; then
        local used_memory=$(echo "$memory_info" | grep "used_memory:" | cut -d: -f2 | tr -d '\r')
        local used_memory_human=$(echo "$memory_info" | grep "used_memory_human:" | cut -d: -f2 | tr -d '\r')
        local used_memory_rss=$(echo "$memory_info" | grep "used_memory_rss:" | cut -d: -f2 | tr -d '\r')
        local mem_fragmentation_ratio=$(echo "$memory_info" | grep "mem_fragmentation_ratio:" | cut -d: -f2 | tr -d '\r')
        
        echo "MEMORY|used_memory:$used_memory|used_memory_human:$used_memory_human|used_memory_rss:$used_memory_rss|mem_fragmentation_ratio:$mem_fragmentation_ratio"
        
        # 检查内存使用警告
        if [ -n "$mem_fragmentation_ratio" ]; then
            local frag_int=$(echo "$mem_fragmentation_ratio" | cut -d. -f1)
            if [ "$frag_int" -gt 2 ]; then
                log_warning "内存碎片率过高: $mem_fragmentation_ratio"
            fi
        fi
    fi
}

# 监控客户端连接
monitor_clients() {
    local clients_info=$(get_redis_info clients)
    
    if [ -n "$clients_info" ]; then
        local connected_clients=$(echo "$clients_info" | grep "connected_clients:" | cut -d: -f2 | tr -d '\r')
        local client_recent_max_input_buffer=$(echo "$clients_info" | grep "client_recent_max_input_buffer:" | cut -d: -f2 | tr -d '\r')
        local client_recent_max_output_buffer=$(echo "$clients_info" | grep "client_recent_max_output_buffer:" | cut -d: -f2 | tr -d '\r')
        local blocked_clients=$(echo "$clients_info" | grep "blocked_clients:" | cut -d: -f2 | tr -d '\r')
        
        echo "CLIENTS|connected_clients:$connected_clients|blocked_clients:$blocked_clients|max_input_buffer:$client_recent_max_input_buffer|max_output_buffer:$client_recent_max_output_buffer"
        
        # 检查连接数警告
        if [ -n "$connected_clients" ] && [ "$connected_clients" -gt 1000 ]; then
            log_warning "客户端连接数过多: $connected_clients"
        fi
    fi
}

# 监控性能统计
monitor_stats() {
    local stats_info=$(get_redis_info stats)
    
    if [ -n "$stats_info" ]; then
        local total_connections_received=$(echo "$stats_info" | grep "total_connections_received:" | cut -d: -f2 | tr -d '\r')
        local total_commands_processed=$(echo "$stats_info" | grep "total_commands_processed:" | cut -d: -f2 | tr -d '\r')
        local instantaneous_ops_per_sec=$(echo "$stats_info" | grep "instantaneous_ops_per_sec:" | cut -d: -f2 | tr -d '\r')
        local total_net_input_bytes=$(echo "$stats_info" | grep "total_net_input_bytes:" | cut -d: -f2 | tr -d '\r')
        local total_net_output_bytes=$(echo "$stats_info" | grep "total_net_output_bytes:" | cut -d: -f2 | tr -d '\r')
        local rejected_connections=$(echo "$stats_info" | grep "rejected_connections:" | cut -d: -f2 | tr -d '\r')
        local expired_keys=$(echo "$stats_info" | grep "expired_keys:" | cut -d: -f2 | tr -d '\r')
        local evicted_keys=$(echo "$stats_info" | grep "evicted_keys:" | cut -d: -f2 | tr -d '\r')
        local keyspace_hits=$(echo "$stats_info" | grep "keyspace_hits:" | cut -d: -f2 | tr -d '\r')
        local keyspace_misses=$(echo "$stats_info" | grep "keyspace_misses:" | cut -d: -f2 | tr -d '\r')
        
        echo "STATS|total_connections:$total_connections_received|total_commands:$total_commands_processed|ops_per_sec:$instantaneous_ops_per_sec|net_input:$total_net_input_bytes|net_output:$total_net_output_bytes|rejected_connections:$rejected_connections|expired_keys:$expired_keys|evicted_keys:$evicted_keys|keyspace_hits:$keyspace_hits|keyspace_misses:$keyspace_misses"
        
        # 计算命中率
        if [ -n "$keyspace_hits" ] && [ -n "$keyspace_misses" ] && [ "$keyspace_hits" -gt 0 ] && [ "$keyspace_misses" -gt 0 ]; then
            local total_requests=$((keyspace_hits + keyspace_misses))
            local hit_rate=$((keyspace_hits * 100 / total_requests))
            echo "HIT_RATE|hit_rate:$hit_rate%|hits:$keyspace_hits|misses:$keyspace_misses"
            
            if [ "$hit_rate" -lt 80 ]; then
                log_warning "缓存命中率较低: $hit_rate%"
            fi
        fi
        
        # 检查拒绝连接
        if [ -n "$rejected_connections" ] && [ "$rejected_connections" -gt 0 ]; then
            log_warning "存在被拒绝的连接: $rejected_connections"
        fi
    fi
}

# 监控持久化状态
monitor_persistence() {
    local persistence_info=$(get_redis_info persistence)
    
    if [ -n "$persistence_info" ]; then
        local rdb_changes_since_last_save=$(echo "$persistence_info" | grep "rdb_changes_since_last_save:" | cut -d: -f2 | tr -d '\r')
        local rdb_bgsave_in_progress=$(echo "$persistence_info" | grep "rdb_bgsave_in_progress:" | cut -d: -f2 | tr -d '\r')
        local rdb_last_save_time=$(echo "$persistence_info" | grep "rdb_last_save_time:" | cut -d: -f2 | tr -d '\r')
        local rdb_last_bgsave_status=$(echo "$persistence_info" | grep "rdb_last_bgsave_status:" | cut -d: -f2 | tr -d '\r')
        local aof_enabled=$(echo "$persistence_info" | grep "aof_enabled:" | cut -d: -f2 | tr -d '\r')
        local aof_rewrite_in_progress=$(echo "$persistence_info" | grep "aof_rewrite_in_progress:" | cut -d: -f2 | tr -d '\r')
        local aof_last_rewrite_time_sec=$(echo "$persistence_info" | grep "aof_last_rewrite_time_sec:" | cut -d: -f2 | tr -d '\r')
        
        echo "PERSISTENCE|rdb_changes:$rdb_changes_since_last_save|rdb_bgsave_progress:$rdb_bgsave_in_progress|rdb_last_save:$rdb_last_save_time|rdb_status:$rdb_last_bgsave_status|aof_enabled:$aof_enabled|aof_rewrite_progress:$aof_rewrite_in_progress|aof_last_rewrite_time:$aof_last_rewrite_time_sec"
        
        # 检查持久化状态
        if [ "$rdb_last_bgsave_status" = "err" ]; then
            log_error "RDB备份失败"
        fi
    fi
}

# 监控数据库键空间
monitor_keyspace() {
    local keyspace_info=$(get_redis_info keyspace)
    
    if [ -n "$keyspace_info" ]; then
        echo "$keyspace_info" | grep "db[0-9]" | while read -r line; do
            if [ -n "$line" ]; then
                local db_name=$(echo "$line" | cut -d: -f1)
                local db_info=$(echo "$line" | cut -d: -f2)
                echo "KEYSPACE|database:$db_name|info:$db_info"
            fi
        done
    fi
}

# 监控慢查询
monitor_slowlog() {
    local redis_cmd=$(build_redis_cmd)
    local slowlog_len=$($redis_cmd slowlog len 2>/dev/null)
    
    if [ -n "$slowlog_len" ] && [ "$slowlog_len" -gt 0 ]; then
        echo "SLOWLOG|count:$slowlog_len"
        
        if [ "$slowlog_len" -gt 10 ]; then
            log_warning "慢查询数量较多: $slowlog_len"
        fi
    fi
}

# 监控复制状态
monitor_replication() {
    local replication_info=$(get_redis_info replication)
    
    if [ -n "$replication_info" ]; then
        local role=$(echo "$replication_info" | grep "role:" | cut -d: -f2 | tr -d '\r')
        local connected_slaves=$(echo "$replication_info" | grep "connected_slaves:" | cut -d: -f2 | tr -d '\r')
        
        echo "REPLICATION|role:$role|connected_slaves:$connected_slaves"
    fi
}

# 生成监控报告
generate_monitor_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "MONITOR_START|timestamp:$timestamp"
    
    monitor_memory
    monitor_clients
    monitor_stats
    monitor_persistence
    monitor_keyspace
    monitor_slowlog
    monitor_replication
    
    echo "MONITOR_END|timestamp:$timestamp"
    echo "----------------------------------------"
}

# 检查告警条件
check_alerts() {
    local redis_cmd=$(build_redis_cmd)
    
    # 检查Redis是否可用
    if ! $redis_cmd ping >/dev/null 2>&1; then
        log_error "Redis服务不可用"
        return 1
    fi
    
    # 检查内存使用
    local memory_info=$(get_redis_info memory)
    if [ -n "$memory_info" ]; then
        local used_memory=$(echo "$memory_info" | grep "used_memory:" | cut -d: -f2 | tr -d '\r')
        local maxmemory=$(echo "$memory_info" | grep "maxmemory:" | cut -d: -f2 | tr -d '\r')
        
        if [ -n "$maxmemory" ] && [ "$maxmemory" -gt 0 ] && [ "$used_memory" -gt 0 ]; then
            local memory_usage=$((used_memory * 100 / maxmemory))
            if [ "$memory_usage" -gt 90 ]; then
                log_error "内存使用率过高: $memory_usage%"
            elif [ "$memory_usage" -gt 80 ]; then
                log_warning "内存使用率较高: $memory_usage%"
            fi
        fi
    fi
}

# 主监控循环
main_monitor() {
    log_info "开始DL引擎Redis监控..."
    log_info "监控间隔: ${MONITOR_INTERVAL}秒"
    log_info "日志文件: $LOG_FILE"
    
    while true; do
        # 生成监控报告
        generate_monitor_report
        
        # 检查告警条件
        check_alerts
        
        # 等待下一次监控
        sleep "$MONITOR_INTERVAL"
    done
}

# 一次性监控
single_monitor() {
    log_info "执行一次性Redis监控..."
    generate_monitor_report
    check_alerts
}

# 显示帮助信息
show_help() {
    echo "DL引擎Redis监控脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  monitor                 持续监控（默认）"
    echo "  once                    一次性监控"
    echo "  help                    显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  REDIS_HOST              Redis主机地址 (默认: 127.0.0.1)"
    echo "  REDIS_PORT              Redis端口 (默认: 6379)"
    echo "  REDIS_PASSWORD          Redis密码"
    echo "  MONITOR_INTERVAL        监控间隔秒数 (默认: 30)"
    echo "  MONITOR_LOG_FILE        监控日志文件"
    echo ""
    echo "示例:"
    echo "  $0                      # 持续监控"
    echo "  $0 once                 # 一次性监控"
    echo "  MONITOR_INTERVAL=60 $0  # 60秒间隔监控"
}

# 信号处理
trap 'log_info "收到退出信号，停止监控..."; exit 0' TERM INT

# 处理命令行参数
case "${1:-}" in
    "monitor"|"")
        main_monitor
        ;;
    "once")
        single_monitor
        ;;
    "help"|"-h"|"--help")
        show_help
        exit 0
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
