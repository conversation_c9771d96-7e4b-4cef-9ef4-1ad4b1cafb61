-- 监控服务数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS monitoring CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE monitoring;

-- 指标表
CREATE TABLE IF NOT EXISTS metrics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  value DECIMAL(10,4) NOT NULL,
  service VARCHAR(50),
  instance VARCHAR(50),
  labels JSON,
  type ENUM('gauge', 'counter', 'histogram', 'summary') DEFAULT 'gauge',
  description VARCHAR(500),
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_name_timestamp (name, timestamp),
  INDEX idx_service_timestamp (service, timestamp)
);

-- 服务状态表
CREATE TABLE IF NOT EXISTS service_status (
  id INT AUTO_INCREMENT PRIMARY KEY,
  serviceName VARCHAR(100) NOT NULL UNIQUE,
  serviceUrl VARCHAR(200),
  status ENUM('healthy', 'unhealthy', 'degraded', 'unknown') DEFAULT 'unknown',
  responseTime INT,
  lastError TEXT,
  consecutiveFailures INT DEFAULT 0,
  lastCheckAt TIMESTAMP,
  lastHealthyAt TIMESTAMP,
  metadata JSON,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_service_updated (serviceName, updatedAt)
);

-- 健康检查表
CREATE TABLE IF NOT EXISTS health_checks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  serviceName VARCHAR(100) NOT NULL,
  checkType ENUM('http', 'tcp', 'database', 'redis', 'elasticsearch', 'custom') NOT NULL,
  status ENUM('pass', 'fail', 'warn') NOT NULL,
  responseTime INT,
  message TEXT,
  error TEXT,
  details JSON,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_service_type_created (serviceName, checkType, createdAt)
);

-- 健康检查历史表
CREATE TABLE IF NOT EXISTS health_check_history (
  id INT AUTO_INCREMENT PRIMARY KEY,
  serviceName VARCHAR(100) NOT NULL,
  status ENUM('pass', 'fail', 'warn') NOT NULL,
  responseTime INT,
  message TEXT,
  error TEXT,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_service_created (serviceName, createdAt)
);

-- 告警规则表
CREATE TABLE IF NOT EXISTS alert_rules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  metric VARCHAR(100) NOT NULL,
  operator ENUM('>', '<', '>=', '<=', '=', '!=') NOT NULL,
  threshold DECIMAL(10,4) NOT NULL,
  duration INT DEFAULT 300,
  severity ENUM('info', 'warning', 'error', 'critical') DEFAULT 'warning',
  enabled BOOLEAN DEFAULT true,
  labels JSON,
  annotations JSON,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 告警表
CREATE TABLE IF NOT EXISTS alerts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  ruleId INT NOT NULL,
  status ENUM('firing', 'resolved') DEFAULT 'firing',
  startsAt TIMESTAMP NOT NULL,
  endsAt TIMESTAMP,
  labels JSON,
  annotations JSON,
  fingerprint VARCHAR(64) NOT NULL UNIQUE,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (ruleId) REFERENCES alert_rules(id) ON DELETE CASCADE,
  INDEX idx_rule_status (ruleId, status),
  INDEX idx_fingerprint (fingerprint)
);

-- 日志条目表
CREATE TABLE IF NOT EXISTS log_entries (
  id INT AUTO_INCREMENT PRIMARY KEY,
  level ENUM('error', 'warn', 'info', 'debug', 'verbose') NOT NULL,
  message TEXT NOT NULL,
  service VARCHAR(100),
  module VARCHAR(100),
  requestId VARCHAR(50),
  userId VARCHAR(50),
  context JSON,
  stack TEXT,
  ip VARCHAR(45),
  userAgent VARCHAR(500),
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_level_timestamp (level, timestamp),
  INDEX idx_service_timestamp (service, timestamp),
  INDEX idx_timestamp (timestamp)
);

-- 通知表
CREATE TABLE IF NOT EXISTS notifications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  type ENUM('email', 'dingtalk', 'wechat', 'slack', 'webhook', 'sms') NOT NULL,
  status ENUM('pending', 'sent', 'failed', 'retry') DEFAULT 'pending',
  alertId INT,
  title VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  recipients JSON NOT NULL,
  config JSON,
  retryCount INT DEFAULT 0,
  maxRetries INT DEFAULT 3,
  error TEXT,
  sentAt TIMESTAMP,
  nextRetryAt TIMESTAMP,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_type_status_created (type, status, createdAt),
  INDEX idx_alert (alertId)
);

-- 通知模板表
CREATE TABLE IF NOT EXISTS notification_templates (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  type ENUM('email', 'dingtalk', 'wechat', 'slack', 'webhook', 'sms') NOT NULL,
  title VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  variables JSON,
  description TEXT,
  enabled BOOLEAN DEFAULT true,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入默认通知模板
INSERT INTO notification_templates (name, type, title, content, variables, description) VALUES
('alert_email', 'email', '系统告警: {{alertName}}', 
 '告警名称: {{alertName}}\n告警级别: {{severity}}\n告警时间: {{startsAt}}\n告警描述: {{description}}\n当前值: {{value}}\n阈值: {{threshold}}',
 '["alertName", "severity", "startsAt", "description", "value", "threshold"]',
 '默认邮件告警模板'),
('alert_dingtalk', 'dingtalk', '系统告警',
 '## 系统告警通知\n\n**告警名称**: {{alertName}}\n\n**告警级别**: {{severity}}\n\n**告警时间**: {{startsAt}}\n\n**告警描述**: {{description}}\n\n**当前值**: {{value}}\n\n**阈值**: {{threshold}}',
 '["alertName", "severity", "startsAt", "description", "value", "threshold"]',
 '默认钉钉告警模板');

-- 创建索引优化查询性能
CREATE INDEX idx_metrics_cleanup ON metrics(timestamp);
CREATE INDEX idx_logs_cleanup ON log_entries(timestamp);
CREATE INDEX idx_health_checks_cleanup ON health_checks(createdAt);
CREATE INDEX idx_notifications_retry ON notifications(status, nextRetryAt);
