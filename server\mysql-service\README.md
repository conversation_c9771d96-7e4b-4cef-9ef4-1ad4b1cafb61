# DL引擎MySQL数据库服务

专为DL（Digital Learning）引擎微服务架构设计的MySQL数据库服务，提供统一的数据存储解决方案。

## 🏗️ 架构特性

### 数据库支持
- **服务注册中心**: `dl_engine_registry`
- **用户服务**: `dl_engine_users`
- **项目服务**: `dl_engine_projects`
- **资产服务**: `dl_engine_assets`
- **渲染服务**: `dl_engine_render`
- **监控服务**: `monitoring`

### 核心功能
- ✅ **自动初始化**: 自动创建所有微服务数据库
- ✅ **性能优化**: 针对微服务架构的MySQL配置优化
- ✅ **健康检查**: 完整的健康监控和状态检查
- ✅ **自动备份**: 定时备份和过期清理
- ✅ **数据恢复**: 灵活的数据恢复机制
- ✅ **权限分离**: 为每个微服务创建独立用户
- ✅ **监控集成**: 内置监控指标和告警规则

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
cd server/mysql-service

# 复制环境变量配置
cp .env.example .env

# 编辑配置文件
vim .env
```

### 2. 启动服务
```bash
# 启动MySQL服务
docker-compose up -d dl-engine-mysql

# 查看启动日志
docker-compose logs -f dl-engine-mysql

# 检查服务状态
docker-compose ps
```

### 3. 验证安装
```bash
# 健康检查
docker exec dl-engine-mysql /usr/local/bin/health-check.sh

# 连接测试
mysql -h localhost -P 3306 -u root -p

# 查看数据库
SHOW DATABASES;
```

## 📊 数据库结构

### 微服务数据库
| 数据库名称 | 服务 | 用途 |
|-----------|------|------|
| `dl_engine_registry` | 服务注册中心 | 服务实例、健康检查、负载均衡 |
| `dl_engine_users` | 用户服务 | 用户信息、认证、权限 |
| `dl_engine_projects` | 项目服务 | 项目、场景、版本管理 |
| `dl_engine_assets` | 资产服务 | 3D模型、纹理、音频资产 |
| `dl_engine_render` | 渲染服务 | 渲染任务、队列、历史 |
| `monitoring` | 监控服务 | 指标、告警、日志、通知 |

### 用户权限
每个微服务都有独立的数据库用户：
- `registry_user` - 服务注册中心用户
- `users_user` - 用户服务用户
- `projects_user` - 项目服务用户
- `assets_user` - 资产服务用户
- `render_user` - 渲染服务用户
- `monitoring_user` - 监控服务用户

## 🔧 配置说明

### 核心配置
```bash
# MySQL基本配置
MYSQL_ROOT_PASSWORD=dl_engine_password_2024
MYSQL_PORT=3306

# 数据目录
MYSQL_DATA_DIR=./data/mysql
MYSQL_BACKUP_DIR=./data/backups
MYSQL_LOG_DIR=./data/logs

# 备份配置
BACKUP_RETENTION_DAYS=7
```

### 性能优化
```bash
# 内存配置（建议2GB+）
INNODB_BUFFER_POOL_SIZE=1G
MAX_CONNECTIONS=500

# 字符集配置
DEFAULT_CHARSET=utf8mb4
DEFAULT_COLLATION=utf8mb4_unicode_ci
```

## 🛠️ 管理工具

### 健康检查
```bash
# 完整健康检查
docker exec dl-engine-mysql /usr/local/bin/health-check.sh full

# 简单连接检查
docker exec dl-engine-mysql /usr/local/bin/health-check.sh simple
```

### 数据备份
```bash
# 全量备份
docker exec dl-engine-mysql /usr/local/bin/backup.sh full

# 备份指定数据库
docker exec dl-engine-mysql /usr/local/bin/backup.sh database monitoring

# 查看备份状态
docker exec dl-engine-mysql /usr/local/bin/backup.sh status

# 清理过期备份
docker exec dl-engine-mysql /usr/local/bin/backup.sh cleanup
```

### 数据恢复
```bash
# 列出可用备份
docker exec dl-engine-mysql /usr/local/bin/restore.sh list

# 恢复最新备份
docker exec dl-engine-mysql /usr/local/bin/restore.sh latest monitoring

# 从指定文件恢复
docker exec dl-engine-mysql /usr/local/bin/restore.sh file /path/to/backup.sql.gz

# 恢复所有数据库
docker exec dl-engine-mysql /usr/local/bin/restore.sh all
```

## 📈 监控指标

### 系统指标
- CPU使用率
- 内存使用率
- 磁盘使用率
- 连接数统计
- 查询性能指标

### 业务指标
- 数据库大小
- 表记录数
- 索引效率
- 慢查询统计

### 告警规则
- CPU使用率 > 80%
- 内存使用率 > 85%
- 磁盘使用率 > 90%
- 连接数 > 400
- 慢查询数量异常

## 🔒 安全配置

### 访问控制
- Root密码强制设置
- 微服务用户权限分离
- 跳过域名解析提高安全性
- 禁用空密码登录

### 网络安全
- 容器网络隔离
- 端口访问控制
- SSL连接支持（可选）

## 🐳 Docker配置

### 服务组件
```yaml
services:
  dl-engine-mysql:     # 主数据库服务
  phpmyadmin:          # Web管理界面（可选）
  mysql-backup:        # 定时备份服务（可选）
```

### 数据卷
```yaml
volumes:
  mysql_data:          # 数据持久化
  mysql_backups:       # 备份存储
  mysql_logs:          # 日志存储
```

### 资源限制
- 内存限制: 2GB
- CPU限制: 1.0核心
- 内存预留: 1GB
- CPU预留: 0.5核心

## 🔄 维护操作

### 日常维护
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs dl-engine-mysql

# 重启服务
docker-compose restart dl-engine-mysql

# 更新配置
docker-compose up -d --force-recreate dl-engine-mysql
```

### 数据迁移
```bash
# 导出数据
docker exec dl-engine-mysql /usr/local/bin/backup.sh full

# 停止服务
docker-compose down

# 更新镜像
docker-compose pull

# 启动服务
docker-compose up -d

# 验证数据
docker exec dl-engine-mysql /usr/local/bin/health-check.sh
```

## 🚨 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 检查端口
   netstat -tulpn | grep 3306
   
   # 检查日志
   docker-compose logs dl-engine-mysql
   ```

2. **启动失败**
   ```bash
   # 检查数据目录权限
   ls -la ./data/mysql
   
   # 检查磁盘空间
   df -h
   
   # 重新初始化
   docker-compose down -v
   docker-compose up -d
   ```

3. **性能问题**
   ```bash
   # 检查资源使用
   docker stats dl-engine-mysql
   
   # 检查慢查询
   docker exec dl-engine-mysql mysql -e "SHOW PROCESSLIST;"
   
   # 优化配置
   vim config/my.cnf
   ```

## 📝 版本信息

- **MySQL版本**: 8.0
- **服务版本**: 1.0.0
- **支持架构**: x86_64, ARM64
- **最低内存**: 1GB
- **推荐内存**: 2GB+

## 🤝 技术支持

如遇到问题，请按以下步骤排查：

1. 检查环境变量配置
2. 查看容器日志
3. 执行健康检查
4. 验证网络连接
5. 检查资源使用情况

更多详细信息请参考DL引擎官方文档。
