@echo off
REM DL引擎Redis服务Windows构建脚本

setlocal enabledelayedexpansion

REM 配置变量
set IMAGE_NAME=dl-engine-redis
set IMAGE_TAG=latest
set BUILD_CONTEXT=.
set DOCKERFILE=Dockerfile

echo [INFO] 开始DL引擎Redis服务构建...

REM 检查Docker是否可用
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装或不可用
    exit /b 1
)

REM 检查必要文件
if not exist "%DOCKERFILE%" (
    echo [ERROR] Dockerfile不存在: %DOCKERFILE%
    exit /b 1
)

if not exist "config\redis.conf" (
    echo [ERROR] 配置文件不存在: config\redis.conf
    exit /b 1
)

echo [INFO] 构建环境检查通过

REM 测试网络连接
echo [INFO] 测试Docker Hub连接...
curl -s --connect-timeout 10 https://docker.io >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Docker Hub连接失败，可能需要配置镜像加速器
    echo [INFO] 建议配置Docker镜像加速器以提高构建速度
    echo.
    echo 推荐的镜像加速器:
    echo   - 阿里云: https://cr.console.aliyun.com/cn-hangzhou/instances/mirrors
    echo   - 腾讯云: https://cloud.tencent.com/document/product/1141/50332
    echo   - 网易云: http://hub-mirror.c.163.com
    echo.
    
    set /p CONTINUE="是否继续构建? (y/N): "
    if /i not "!CONTINUE!"=="y" (
        echo [INFO] 构建已取消
        exit /b 0
    )
) else (
    echo [SUCCESS] Docker Hub连接正常
)

REM 尝试拉取基础镜像
echo [INFO] 尝试拉取Redis基础镜像...
docker pull redis:alpine >nul 2>&1
if errorlevel 1 (
    echo [WARNING] 无法拉取redis:alpine镜像
    echo [INFO] 尝试使用离线构建方案...
    
    set /p USE_OFFLINE="是否使用离线构建? (y/N): "
    if /i "!USE_OFFLINE!"=="y" (
        set DOCKERFILE=Dockerfile.offline
        echo [INFO] 切换到离线构建模式
    ) else (
        echo [ERROR] 无法获取基础镜像，构建失败
        exit /b 1
    )
) else (
    echo [SUCCESS] Redis基础镜像获取成功
)

REM 构建镜像
echo [INFO] 开始构建Redis镜像...
echo [INFO] 镜像名称: %IMAGE_NAME%:%IMAGE_TAG%
echo [INFO] 使用Dockerfile: %DOCKERFILE%

docker build ^
    --file %DOCKERFILE% ^
    --tag %IMAGE_NAME%:%IMAGE_TAG% ^
    --label "com.dl-engine.service=redis" ^
    --label "com.dl-engine.version=1.0.0" ^
    --label "com.dl-engine.build-date=%date% %time%" ^
    --label "com.dl-engine.description=DL引擎Redis缓存服务" ^
    %BUILD_CONTEXT%

if errorlevel 1 (
    echo [ERROR] 镜像构建失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查网络连接
    echo 2. 配置Docker镜像加速器
    echo 3. 使用离线构建模式
    echo 4. 检查防火墙设置
    echo.
    exit /b 1
)

echo [SUCCESS] 镜像构建成功: %IMAGE_NAME%:%IMAGE_TAG%

REM 显示镜像信息
echo.
echo ========================================
echo 构建完成信息:
echo ========================================
echo 镜像名称: %IMAGE_NAME%:%IMAGE_TAG%
echo 构建时间: %date% %time%

REM 获取镜像信息
for /f "tokens=*" %%i in ('docker images %IMAGE_NAME%:%IMAGE_TAG% --format "{{.ID}}"') do set IMAGE_ID=%%i
for /f "tokens=*" %%i in ('docker images %IMAGE_NAME%:%IMAGE_TAG% --format "{{.Size}}"') do set IMAGE_SIZE=%%i

echo 镜像ID: %IMAGE_ID%
echo 镜像大小: %IMAGE_SIZE%
echo 使用Dockerfile: %DOCKERFILE%
echo.
echo 使用方法:
echo   docker run -d \
echo     --name dl-engine-redis \
echo     -p 6379:6379 \
echo     %IMAGE_NAME%:%IMAGE_TAG%
echo.
echo 或使用docker-compose:
echo   docker-compose up -d dl-engine-redis
echo ========================================

REM 询问是否运行测试
set /p RUN_TEST="是否运行镜像测试? (y/N): "
if /i "%RUN_TEST%"=="y" (
    echo [INFO] 开始测试镜像...
    
    REM 创建测试容器
    set TEST_CONTAINER=test-%IMAGE_NAME%-%RANDOM%
    
    echo [INFO] 创建测试容器: !TEST_CONTAINER!
    
    docker run -d ^
        --name !TEST_CONTAINER! ^
        -p 16379:6379 ^
        %IMAGE_NAME%:%IMAGE_TAG%
    
    if errorlevel 1 (
        echo [ERROR] 测试容器启动失败
        exit /b 1
    )
    
    echo [INFO] 等待Redis服务启动...
    timeout /t 10 /nobreak >nul
    
    REM 测试Redis连接
    docker exec !TEST_CONTAINER! redis-cli ping >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Redis连接测试失败
    ) else (
        echo [SUCCESS] Redis连接测试通过
    )
    
    REM 测试健康检查
    docker exec !TEST_CONTAINER! /usr/local/bin/health-check.sh simple >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] 健康检查测试失败
    ) else (
        echo [SUCCESS] 健康检查测试通过
    )
    
    REM 清理测试容器
    docker stop !TEST_CONTAINER! >nul 2>&1
    docker rm !TEST_CONTAINER! >nul 2>&1
    echo [SUCCESS] 测试容器已清理
)

echo [SUCCESS] Redis服务构建完成

REM 显示网络优化建议
echo.
echo ========================================
echo 网络优化建议:
echo ========================================
echo 1. 配置Docker镜像加速器以提高构建速度
echo 2. 使用企业内部镜像仓库
echo 3. 预先下载常用基础镜像
echo 4. 配置HTTP代理（如果需要）
echo 5. 检查防火墙和网络策略
echo ========================================

pause
