export default () => ({
  // 应用配置
  app: {
    port: parseInt(process.env.PORT, 10) || 3100,
    environment: process.env.NODE_ENV || 'development',
    corsOrigin: process.env.CORS_ORIGIN || '*',
  },

  // 数据库配置
  database: {
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 3306,
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_DATABASE || 'monitoring',
    synchronize: process.env.DB_SYNCHRONIZE === 'true',
    logging: process.env.DB_LOGGING === 'true',
  },

  // Elasticsearch配置
  elasticsearch: {
    enabled: process.env.ELASTICSEARCH_ENABLED === 'true',
    node: process.env.ELASTICSEARCH_NODE || 'http://localhost:9200',
    index: process.env.ELASTICSEARCH_INDEX || 'logs',
    username: process.env.ELASTICSEARCH_USERNAME,
    password: process.env.ELASTICSEARCH_PASSWORD,
  },

  // 邮件配置
  mail: {
    host: process.env.MAIL_HOST,
    port: parseInt(process.env.MAIL_PORT, 10) || 587,
    secure: process.env.MAIL_SECURE === 'true',
    auth: {
      user: process.env.MAIL_USER,
      pass: process.env.MAIL_PASS,
    },
    from: process.env.MAIL_FROM,
  },

  // 通知配置
  notifications: {
    dingtalk: {
      webhook: process.env.DINGTALK_WEBHOOK_URL,
      secret: process.env.DINGTALK_SECRET,
    },
    wechat: {
      webhook: process.env.WECHAT_WEBHOOK_URL,
    },
    slack: {
      webhook: process.env.SLACK_WEBHOOK_URL,
    },
    webhook: {
      url: process.env.WEBHOOK_URL,
    },
  },

  // 告警配置
  alerts: {
    checkInterval: parseInt(process.env.ALERT_CHECK_INTERVAL, 10) || 30000,
    retentionDays: parseInt(process.env.ALERT_RETENTION_DAYS, 10) || 30,
  },

  // 监控配置
  monitoring: {
    metricsRetentionDays: parseInt(process.env.METRICS_RETENTION_DAYS, 10) || 7,
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL, 10) || 10000,
    healthCheckTimeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT, 10) || 5000,
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    retentionDays: parseInt(process.env.LOG_RETENTION_DAYS, 10) || 30,
    maxSize: process.env.LOG_MAX_SIZE || '100m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES, 10) || 10,
  },

  // 服务注册配置
  serviceRegistry: {
    url: process.env.SERVICE_REGISTRY_URL || 'http://localhost:4010',
  },
});
