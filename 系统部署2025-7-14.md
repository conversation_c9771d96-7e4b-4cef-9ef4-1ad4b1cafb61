# DL（Digital Learning）引擎系统部署文档

**文档版本**: 2025-7-14  
**系统版本**: v0.1.0  
**部署方式**: Docker Compose  

## 1. 系统概述

DL（Digital Learning）引擎是一个强大的3D引擎和编辑器平台，采用现代微服务架构设计，支持大规模并发用户访问。系统由三大核心部分组成：

### 1.1 底层引擎（Engine）
- **技术栈**: TypeScript + Three.js + Cannon.js
- **架构模式**: ECS（Entity-Component-System）
- **核心功能**: 3D渲染、物理模拟、动画系统、粒子系统、音频处理
- **特性**: WebGL硬件加速、PBR渲染、实时阴影、后处理效果

### 1.2 编辑器前端（Editor）
- **技术栈**: React + Redux + Ant Design + Vite
- **端口**: 80 (HTTP)
- **功能**: 可视化3D场景编辑、实时预览、协作编辑、资产管理
- **特性**: 响应式设计、多语言支持、插件系统

### 1.3 服务器端微服务（Server）
- **技术栈**: Nest.js + TypeORM + MySQL + Redis
- **架构**: 微服务架构 + 服务注册发现
- **核心服务**:
  - 服务注册中心 (3010/4010)
  - API网关 (3000)
  - 用户服务 (3001/4001)
  - 项目服务 (3002/4002)
  - 资产服务 (3003/4003)
  - 渲染服务 (3004/4004)
  - 协作服务 (3005-3007)
  - 游戏服务器 (3030/3031)

### 1.4 监控和运维层
- **技术栈**: ELK Stack + Prometheus + Grafana
- **监控服务**: 系统监控和告警 (3100)
- **日志分析**: Elasticsearch (9200) + Kibana (5601)
- **指标收集**: Prometheus (9090) + Node Exporter (9100)
- **可视化**: Grafana (3000) 监控面板

## 2. 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层                                │
├─────────────────────────────────────────────────────────────┤
│  编辑器 (React + Redux + Ant Design)                       │
│  端口: 80 (HTTP)                                           │
│  功能: 3D场景编辑、实时预览、协作编辑                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      API网关层                              │
├─────────────────────────────────────────────────────────────┤
│  API网关 (Nest.js)                                         │
│  端口: 3000 (HTTP)                                         │
│  功能: 路由、认证、限流、负载均衡、API聚合                    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     微服务层                                │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │ 用户服务    │ │ 项目服务    │ │ 资产服务    │ │ 渲染服务    │ │
│ │ 3001/4001   │ │ 3002/4002   │ │ 3003/4003   │ │ 3004/4004   │ │
│ │ 用户管理    │ │ 项目管理    │ │ 文件管理    │ │ 3D渲染      │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                │
│ │ 协作服务    │ │ 游戏服务器  │ │ 服务注册    │                │
│ │ 3005-3007   │ │ 3030/3031   │ │ 3010/4010   │                │
│ │ 实时协作    │ │ 游戏实例    │ │ 服务发现    │                │
│ └─────────────┘ └─────────────┘ └─────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据层                                 │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│ │ MySQL 8.0   │ │ Redis 7.0   │ │ 文件存储    │              │
│ │ 端口: 3306  │ │ 端口: 6379  │ │ (Volumes)   │              │
│ │ 主数据库    │ │ 缓存/会话   │ │ 资产文件    │              │
│ └─────────────┘ └─────────────┘ └─────────────┘              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    监控运维层                               │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │ Prometheus  │ │ Grafana     │ │ Elasticsearch│ │ Kibana      │ │
│ │ 9090        │ │ 3000        │ │ 9200        │ │ 5601        │ │
│ │ 指标收集    │ │ 监控面板    │ │ 日志存储    │ │ 日志分析    │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 3. 系统要求

### 3.1 硬件要求
- **CPU**: 4核心以上，推荐8核心
- **内存**: 8GB以上，推荐16GB
- **存储**: 50GB以上可用空间，推荐SSD
- **网络**: 稳定的互联网连接

### 3.2 软件要求
- **操作系统**: Linux (Ubuntu 20.04+) / Windows 10+ / macOS 10.15+
- **Docker**: 20.10.0+
- **Docker Compose**: 2.0.0+
- **Node.js**: 18.0+ (开发环境)
- **Git**: 2.30+ (代码管理)

### 3.3 端口要求
系统使用以下端口，请确保这些端口未被占用：

| 服务 | 端口 | 协议 | 说明 |
|------|------|------|------|
| 编辑器 | 80 | HTTP | 前端应用 |
| API网关 | 3000 | HTTP | 统一API入口 |
| 用户服务 | 3001/4001 | TCP/HTTP | 用户管理 |
| 项目服务 | 3002/4002 | TCP/HTTP | 项目管理 |
| 资产服务 | 3003/4003 | TCP/HTTP | 资产管理 |
| 渲染服务 | 3004/4004 | TCP/HTTP | 渲染处理 |
| 协作服务 | 3005-3007 | WebSocket | 实时协作 |
| 服务注册 | 3010/4010 | TCP/HTTP | 服务发现 |
| 游戏服务器 | 3030/3031 | TCP/HTTP | 游戏实例 |
| 监控服务 | 3100 | HTTP | 系统监控 |
| MySQL | 3306 | TCP | 数据库 |
| Redis | 6379 | TCP | 缓存 |
| cAdvisor | 8080 | HTTP | 容器监控 |
| Prometheus | 9090 | HTTP | 指标收集 |
| Node Exporter | 9100 | HTTP | 系统指标 |
| Elasticsearch | 9200 | HTTP | 日志存储 |
| Kibana | 5601 | HTTP | 日志分析 |

## 4. 部署准备

### 4.1 安装Docker和Docker Compose

**Ubuntu/Debian:**
```bash
# 更新包索引
sudo apt update

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将用户添加到docker组
sudo usermod -aG docker $USER
```

**CentOS/RHEL:**
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
```

**Windows:**
1. 下载并安装 Docker Desktop for Windows
2. 启用WSL2支持
3. 重启系统

**macOS:**
1. 下载并安装 Docker Desktop for Mac
2. 启动Docker Desktop应用

### 4.2 获取源代码
```bash
git clone https://github.com/your-username/dl-engine.git
cd dl-engine
```

### 4.3 环境变量配置
创建 `.env` 文件：
```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=dl_engine_password_2024

# JWT配置
JWT_SECRET=dl_engine_jwt_secret_key_2024_very_secure

# 服务配置
NODE_ENV=production

# 网络配置
CORS_ORIGIN=*

# 文件上传配置
MAX_FILE_SIZE=104857600

# 监控配置
ELASTICSEARCH_ENABLED=true
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_mail_password

# 协作服务配置
ENABLE_COMPRESSION=true
COMPRESSION_LEVEL=6
MAX_BATCH_SIZE=50
MAX_BATCH_WAIT_TIME=50
```

### 4.4 检查系统环境
```bash
# 检查Docker版本
docker --version
docker-compose --version

# 检查Docker服务状态
sudo systemctl status docker

# 检查端口占用
netstat -tulpn | grep -E ':(80|3000|3001|3002|3003|3004|3005|3006|3007|3010|3030|3031|3306|6379)\s'

# 检查系统资源
free -h
df -h
```

### 4.5 Docker Compose文件说明

项目包含两个主要的Docker Compose文件：

**docker-compose.yml** - 主要服务配置
- 包含所有核心微服务
- 数据库和缓存服务
- 前端编辑器
- 网络和数据卷配置

**docker-compose.monitoring.yml** - 监控服务配置
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Prometheus + Grafana
- 系统监控组件

### 4.6 数据卷说明

系统使用以下Docker卷进行数据持久化：
- `mysql_data`: MySQL数据库文件
- `redis_data`: Redis持久化数据
- `asset_uploads`: 用户上传的资产文件
- `render_outputs`: 渲染输出文件
- `elasticsearch-data`: 日志数据
- `prometheus-data`: 监控指标数据
- `grafana-data`: Grafana配置和仪表板

## 5. 部署步骤

### 5.1 自动化部署（推荐）
使用提供的启动脚本：
```bash
# 赋予执行权限
chmod +x scripts/start-services.sh

# 启动所有服务
./scripts/start-services.sh

# 启动服务并包含监控
./scripts/start-services.sh --with-monitoring
```

### 5.2 手动部署
如果需要手动控制部署过程：

#### 步骤1: 启动基础设施
```bash
# 启动数据库和缓存
docker-compose up -d mysql redis

# 等待MySQL启动完成
docker-compose logs -f mysql
```

#### 步骤2: 启动服务注册中心
```bash
docker-compose up -d service-registry

# 检查服务状态
curl http://localhost:4010/health
```

#### 步骤3: 启动业务微服务
```bash
docker-compose up -d user-service project-service asset-service render-service

# 检查服务状态
curl http://localhost:4001/health
curl http://localhost:4002/health
curl http://localhost:4003/health
curl http://localhost:4004/health
```

#### 步骤4: 启动协作服务
```bash
docker-compose up -d collaboration-service-1 collaboration-service-2
docker-compose up -d collaboration-load-balancer

# 检查协作服务
curl http://localhost:3007/health
```

#### 步骤5: 启动API网关
```bash
docker-compose up -d api-gateway

# 检查API网关
curl http://localhost:3000/api/health
```

#### 步骤6: 启动前端编辑器
```bash
docker-compose up -d editor

# 访问编辑器
open http://localhost
```

### 5.3 启动监控服务（可选）
```bash
docker-compose -f docker-compose.monitoring.yml up -d
```

### 5.4 服务启动顺序说明

系统采用分层启动策略，确保依赖关系正确：

1. **基础设施层** (MySQL, Redis)
   - 提供数据存储和缓存服务
   - 启动时间: 15-30秒

2. **服务注册层** (Service Registry)
   - 提供服务发现和注册功能
   - 依赖: MySQL
   - 启动时间: 10-15秒

3. **业务服务层** (User, Project, Asset, Render Services)
   - 核心业务逻辑处理
   - 依赖: MySQL, Service Registry
   - 启动时间: 20-30秒

4. **协作服务层** (Collaboration Services + Load Balancer)
   - 实时协作功能
   - 依赖: Redis, Service Registry
   - 启动时间: 15-20秒

5. **网关层** (API Gateway)
   - 统一API入口
   - 依赖: 所有业务服务
   - 启动时间: 10-15秒

6. **前端层** (Editor)
   - 用户界面
   - 依赖: API Gateway
   - 启动时间: 10-15秒

### 5.5 健康检查机制

每个服务都配置了健康检查：
- **检查间隔**: 10秒
- **超时时间**: 5秒
- **重试次数**: 3-5次
- **检查方式**: HTTP健康检查端点

## 6. 服务验证

### 6.1 健康检查
```bash
# 检查所有容器状态
docker-compose ps

# 检查服务健康状态
curl http://localhost:3000/api/health
curl http://localhost:4010/health
```

### 6.2 功能测试
1. **前端访问**: 打开 http://localhost
2. **API文档**: 访问 http://localhost:3000/api/docs
3. **用户注册**: 通过前端界面注册新用户
4. **项目创建**: 创建新的3D项目
5. **协作测试**: 多用户同时编辑同一项目

### 6.3 监控面板
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Kibana**: http://localhost:5601
- **Elasticsearch**: http://localhost:9200
- **cAdvisor**: http://localhost:8080

### 6.4 服务依赖关系验证
```bash
# 检查服务注册中心
curl -s http://localhost:4010/services | jq .

# 检查API网关路由
curl -s http://localhost:3000/api/routes

# 检查数据库连接
docker exec dl-engine-mysql mysql -u root -p[password] -e "SHOW DATABASES;"

# 检查Redis连接
docker exec dl-engine-redis redis-cli ping
```

## 7. 常见问题排查

### 7.1 端口冲突
```bash
# 查找占用端口的进程
lsof -i :3000
kill -9 <PID>
```

### 7.2 数据库连接失败
```bash
# 检查MySQL容器日志
docker-compose logs mysql

# 手动连接测试
docker exec -it dl-engine-mysql mysql -u root -p
```

### 7.3 服务启动失败
```bash
# 查看特定服务日志
docker-compose logs [service-name]

# 重启服务
docker-compose restart [service-name]
```

### 7.4 内存不足
```bash
# 检查系统资源
docker stats

# 清理未使用的镜像和容器
docker system prune -a
```

## 8. 维护操作

### 8.1 停止服务
```bash
# 优雅停止所有服务
./scripts/stop-services.sh

# 或手动停止
docker-compose down

# 停止并删除数据卷（谨慎使用）
docker-compose down -v

# 停止监控服务
docker-compose -f docker-compose.monitoring.yml down
```

### 8.2 更新服务
```bash
# 拉取最新代码
git pull origin main

# 重新构建特定服务
docker-compose build [service-name]

# 重新构建并启动所有服务
docker-compose up -d --build

# 滚动更新（零停机）
docker-compose up -d --no-deps [service-name]
```

### 8.3 备份数据
```bash
# 创建备份目录
mkdir -p backup/$(date +%Y%m%d)

# 备份MySQL数据
docker exec dl-engine-mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} --all-databases > backup/$(date +%Y%m%d)/mysql_backup.sql

# 备份Redis数据
docker exec dl-engine-redis redis-cli BGSAVE
docker cp dl-engine-redis:/data/dump.rdb backup/$(date +%Y%m%d)/

# 备份上传文件
docker cp dl-engine-asset-service:/app/uploads backup/$(date +%Y%m%d)/uploads

# 备份渲染输出
docker cp dl-engine-render-service:/app/renders backup/$(date +%Y%m%d)/renders

# 创建完整备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

echo "开始备份..."
docker exec dl-engine-mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} --all-databases > $BACKUP_DIR/mysql_backup.sql
docker exec dl-engine-redis redis-cli BGSAVE
docker cp dl-engine-redis:/data/dump.rdb $BACKUP_DIR/
docker cp dl-engine-asset-service:/app/uploads $BACKUP_DIR/uploads
docker cp dl-engine-render-service:/app/renders $BACKUP_DIR/renders

echo "备份完成: $BACKUP_DIR"
EOF

chmod +x backup.sh
```

### 8.4 日志管理
```bash
# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f [service-name]

# 查看最近的日志
docker-compose logs --tail=100 [service-name]

# 日志轮转配置
cat > /etc/docker/daemon.json << EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

# 重启Docker服务
sudo systemctl restart docker

# 清理旧日志
docker system prune -f
```

### 8.5 数据恢复
```bash
# 恢复MySQL数据
docker exec -i dl-engine-mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} < backup/mysql_backup.sql

# 恢复Redis数据
docker cp backup/dump.rdb dl-engine-redis:/data/
docker-compose restart redis

# 恢复文件数据
docker cp backup/uploads dl-engine-asset-service:/app/
docker cp backup/renders dl-engine-render-service:/app/
```

## 9. 性能优化

### 9.1 资源限制
在 `docker-compose.yml` 中添加资源限制：
```yaml
services:
  mysql:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

### 9.2 缓存优化
- 启用Redis持久化
- 配置适当的缓存过期时间
- 使用CDN加速静态资源

### 9.3 数据库优化
- 配置MySQL连接池
- 启用查询缓存
- 定期优化数据库表

## 10. 安全配置

### 10.1 网络安全
```bash
# 配置防火墙规则（Ubuntu/Debian）
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp
sudo ufw enable

# 配置SSL/TLS证书（使用Let's Encrypt）
sudo apt install certbot
sudo certbot --nginx -d your-domain.com

# 配置Nginx反向代理
cat > nginx.conf << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF
```

### 10.2 认证安全
```bash
# 生成强JWT密钥
openssl rand -base64 64

# 配置密码策略
cat >> .env << 'EOF'
# 密码策略
PASSWORD_MIN_LENGTH=12
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true

# JWT配置
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
EOF
```

### 10.3 数据安全
```bash
# 数据库加密配置
cat > mysql-ssl.cnf << 'EOF'
[mysqld]
ssl-ca=/etc/mysql/ssl/ca-cert.pem
ssl-cert=/etc/mysql/ssl/server-cert.pem
ssl-key=/etc/mysql/ssl/server-key.pem
require_secure_transport=ON
EOF

# Redis安全配置
cat > redis-security.conf << 'EOF'
requirepass your_redis_password
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
EOF
```

### 10.4 容器安全
```bash
# 使用非root用户运行容器
# 在Dockerfile中添加：
# RUN addgroup -g 1001 -S nodejs
# RUN adduser -S nodejs -u 1001
# USER nodejs

# 限制容器资源
cat >> docker-compose.yml << 'EOF'
services:
  mysql:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
EOF
```

## 11. 生产环境优化

### 11.1 性能优化
```bash
# 数据库优化
cat > mysql-performance.cnf << 'EOF'
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 128M
max_connections = 200
EOF

# Redis优化
cat > redis-performance.conf << 'EOF'
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
EOF
```

### 11.2 监控告警
```bash
# 配置Grafana告警
cat > grafana-alerts.json << 'EOF'
{
  "alert": {
    "name": "High CPU Usage",
    "frequency": "10s",
    "conditions": [
      {
        "query": {
          "queryType": "",
          "refId": "A"
        },
        "reducer": {
          "type": "last",
          "params": []
        },
        "evaluator": {
          "params": [80],
          "type": "gt"
        }
      }
    ]
  }
}
EOF
```

### 11.3 自动化运维
```bash
# 创建健康检查脚本
cat > health-check.sh << 'EOF'
#!/bin/bash
SERVICES=("api-gateway:3000" "user-service:4001" "project-service:4002")

for service in "${SERVICES[@]}"; do
    IFS=':' read -r name port <<< "$service"
    if curl -f -s http://localhost:$port/health > /dev/null; then
        echo "✓ $name is healthy"
    else
        echo "✗ $name is unhealthy"
        # 重启服务
        docker-compose restart $name
    fi
done
EOF

chmod +x health-check.sh

# 添加到crontab
echo "*/5 * * * * /path/to/health-check.sh" | crontab -
```

## 12. 故障恢复

### 12.1 灾难恢复计划
1. **数据备份策略**: 每日自动备份，异地存储
2. **服务恢复顺序**: 按依赖关系逐层恢复
3. **监控告警**: 实时监控关键指标
4. **文档维护**: 保持恢复文档更新

### 12.2 常见故障处理
```bash
# 服务无响应
docker-compose restart [service-name]

# 数据库连接失败
docker-compose logs mysql
docker-compose restart mysql

# 内存不足
docker stats
docker system prune -f

# 磁盘空间不足
df -h
docker volume prune -f
```

---

## 部署完成

**部署完成后，您可以通过以下地址访问系统：**

- **主应用**: http://localhost (或 https://your-domain.com)
- **API文档**: http://localhost:3000/api/docs
- **监控面板**: http://localhost:3000 (Grafana)
- **系统指标**: http://localhost:9090 (Prometheus)
- **日志分析**: http://localhost:5601 (Kibana)

**技术支持**:
- 查看日志: `docker-compose logs -f`
- 健康检查: `./scripts/check-services.sh`
- 问题反馈: 请联系技术支持团队

**注意事项**:
1. 生产环境请修改默认密码和密钥
2. 定期备份重要数据
3. 监控系统资源使用情况
4. 及时更新系统和依赖包
