# DL引擎MySQL数据库服务
# 基于MySQL 8.0，为DL引擎微服务架构提供统一的数据库服务

FROM mysql:8.0

# 维护者信息
LABEL maintainer="DL Engine Team"
LABEL description="DL引擎统一MySQL数据库服务"
LABEL version="1.0.0"

# 设置环境变量（密码将通过docker-compose或运行时传入）
# 注意：MYSQL_ROOT_PASSWORD 将在运行时通过环境变量设置
# 这些配置变量不包含敏感信息，仅用于MySQL安全配置

# 设置字符集和排序规则
ENV MYSQL_CHARSET=utf8mb4
ENV MYSQL_COLLATION=utf8mb4_unicode_ci

# 创建数据目录
RUN mkdir -p /var/lib/mysql-files

# 复制自定义MySQL配置文件
COPY config/my.cnf /etc/mysql/conf.d/dl-engine.cnf

# 复制数据库初始化脚本
COPY init-scripts/ /docker-entrypoint-initdb.d/

# 复制健康检查脚本
COPY scripts/health-check.sh /usr/local/bin/health-check.sh
RUN chmod +x /usr/local/bin/health-check.sh

# 复制备份脚本
COPY scripts/backup.sh /usr/local/bin/backup.sh
RUN chmod +x /usr/local/bin/backup.sh

# 复制恢复脚本
COPY scripts/restore.sh /usr/local/bin/restore.sh
RUN chmod +x /usr/local/bin/restore.sh

# 复制环境变量验证脚本
COPY scripts/validate-env.sh /usr/local/bin/validate-env.sh
RUN chmod +x /usr/local/bin/validate-env.sh

# 复制自定义入口脚本
COPY scripts/docker-entrypoint.sh /usr/local/bin/dl-engine-entrypoint.sh
RUN chmod +x /usr/local/bin/dl-engine-entrypoint.sh

# 设置数据目录权限
RUN chown -R mysql:mysql /var/lib/mysql /var/lib/mysql-files

# 暴露MySQL端口
EXPOSE 3306

# 设置数据卷
VOLUME ["/var/lib/mysql", "/var/lib/mysql-files", "/var/log/mysql"]

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/health-check.sh

# 启动MySQL服务
CMD ["mysqld"]
