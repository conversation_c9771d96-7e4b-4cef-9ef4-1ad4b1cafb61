#!/bin/bash

# DL引擎Redis健康检查脚本
# 用于Docker健康检查和监控

set -e

# 配置变量
REDIS_HOST=${REDIS_HOST:-"127.0.0.1"}
REDIS_PORT=${REDIS_PORT:-"6379"}
REDIS_PASSWORD=${REDIS_PASSWORD:-""}
TIMEOUT=${HEALTH_CHECK_TIMEOUT:-"5"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 构建Redis连接命令
build_redis_cmd() {
    local cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
    
    if [ -n "$REDIS_PASSWORD" ]; then
        cmd="$cmd -a $REDIS_PASSWORD"
    fi
    
    echo "$cmd"
}

# 检查Redis进程
check_redis_process() {
    log_info "检查Redis进程..."
    
    if pgrep redis-server > /dev/null; then
        log_success "Redis进程运行正常"
        return 0
    else
        log_error "Redis进程未运行"
        return 1
    fi
}

# 检查Redis端口
check_redis_port() {
    log_info "检查Redis端口 $REDIS_PORT..."
    
    if netstat -ln 2>/dev/null | grep ":$REDIS_PORT " > /dev/null; then
        log_success "Redis端口 $REDIS_PORT 监听正常"
        return 0
    else
        log_error "Redis端口 $REDIS_PORT 未监听"
        return 1
    fi
}

# 检查Redis连接
check_redis_connection() {
    log_info "检查Redis连接..."
    
    local redis_cmd=$(build_redis_cmd)
    
    if timeout "$TIMEOUT" $redis_cmd ping 2>/dev/null | grep -q "PONG"; then
        log_success "Redis连接正常"
        return 0
    else
        log_error "Redis连接失败"
        return 1
    fi
}

# 检查Redis信息
check_redis_info() {
    log_info "检查Redis服务信息..."
    
    local redis_cmd=$(build_redis_cmd)
    local info_output
    
    if info_output=$(timeout "$TIMEOUT" $redis_cmd info server 2>/dev/null); then
        local version=$(echo "$info_output" | grep "redis_version:" | cut -d: -f2 | tr -d '\r')
        local uptime=$(echo "$info_output" | grep "uptime_in_seconds:" | cut -d: -f2 | tr -d '\r')
        local mode=$(echo "$info_output" | grep "redis_mode:" | cut -d: -f2 | tr -d '\r')
        
        log_success "Redis服务信息获取成功"
        log_info "Redis版本: $version"
        log_info "运行时间: $uptime 秒"
        log_info "运行模式: $mode"
        return 0
    else
        log_error "Redis服务信息获取失败"
        return 1
    fi
}

# 检查Redis内存使用
check_redis_memory() {
    log_info "检查Redis内存使用..."
    
    local redis_cmd=$(build_redis_cmd)
    local memory_info
    
    if memory_info=$(timeout "$TIMEOUT" $redis_cmd info memory 2>/dev/null); then
        local used_memory=$(echo "$memory_info" | grep "used_memory_human:" | cut -d: -f2 | tr -d '\r')
        local used_memory_peak=$(echo "$memory_info" | grep "used_memory_peak_human:" | cut -d: -f2 | tr -d '\r')
        local memory_fragmentation=$(echo "$memory_info" | grep "mem_fragmentation_ratio:" | cut -d: -f2 | tr -d '\r')
        
        log_success "Redis内存信息获取成功"
        log_info "当前内存使用: $used_memory"
        log_info "峰值内存使用: $used_memory_peak"
        log_info "内存碎片率: $memory_fragmentation"
        
        # 检查内存碎片率
        if [ -n "$memory_fragmentation" ]; then
            local frag_int=$(echo "$memory_fragmentation" | cut -d. -f1)
            if [ "$frag_int" -gt 2 ]; then
                log_warning "内存碎片率较高: $memory_fragmentation"
            fi
        fi
        
        return 0
    else
        log_error "Redis内存信息获取失败"
        return 1
    fi
}

# 检查Redis客户端连接
check_redis_clients() {
    log_info "检查Redis客户端连接..."
    
    local redis_cmd=$(build_redis_cmd)
    local clients_info
    
    if clients_info=$(timeout "$TIMEOUT" $redis_cmd info clients 2>/dev/null); then
        local connected_clients=$(echo "$clients_info" | grep "connected_clients:" | cut -d: -f2 | tr -d '\r')
        local blocked_clients=$(echo "$clients_info" | grep "blocked_clients:" | cut -d: -f2 | tr -d '\r')
        
        log_success "Redis客户端信息获取成功"
        log_info "连接客户端数: $connected_clients"
        log_info "阻塞客户端数: $blocked_clients"
        
        # 检查连接数是否过多
        if [ -n "$connected_clients" ] && [ "$connected_clients" -gt 1000 ]; then
            log_warning "客户端连接数较多: $connected_clients"
        fi
        
        return 0
    else
        log_error "Redis客户端信息获取失败"
        return 1
    fi
}

# 检查Redis持久化
check_redis_persistence() {
    log_info "检查Redis持久化状态..."
    
    local redis_cmd=$(build_redis_cmd)
    local persistence_info
    
    if persistence_info=$(timeout "$TIMEOUT" $redis_cmd info persistence 2>/dev/null); then
        local rdb_last_save=$(echo "$persistence_info" | grep "rdb_last_save_time:" | cut -d: -f2 | tr -d '\r')
        local aof_enabled=$(echo "$persistence_info" | grep "aof_enabled:" | cut -d: -f2 | tr -d '\r')
        local rdb_last_bgsave_status=$(echo "$persistence_info" | grep "rdb_last_bgsave_status:" | cut -d: -f2 | tr -d '\r')
        
        log_success "Redis持久化信息获取成功"
        
        if [ "$rdb_last_save" != "0" ]; then
            local last_save_date=$(date -d "@$rdb_last_save" 2>/dev/null || echo "无法解析")
            log_info "RDB最后保存: $last_save_date"
        fi
        
        log_info "AOF启用状态: $aof_enabled"
        log_info "RDB最后备份状态: $rdb_last_bgsave_status"
        
        if [ "$rdb_last_bgsave_status" = "err" ]; then
            log_warning "RDB最后备份失败"
        fi
        
        return 0
    else
        log_error "Redis持久化信息获取失败"
        return 1
    fi
}

# 检查Redis数据库
check_redis_databases() {
    log_info "检查Redis数据库使用情况..."
    
    local redis_cmd=$(build_redis_cmd)
    local keyspace_info
    
    if keyspace_info=$(timeout "$TIMEOUT" $redis_cmd info keyspace 2>/dev/null); then
        if echo "$keyspace_info" | grep -q "db[0-9]"; then
            log_success "Redis数据库信息获取成功"
            echo "$keyspace_info" | grep "db[0-9]" | while read -r line; do
                log_info "数据库状态: $line"
            done
        else
            log_info "所有数据库为空"
        fi
        return 0
    else
        log_error "Redis数据库信息获取失败"
        return 1
    fi
}

# 测试基本操作
test_redis_operations() {
    log_info "测试Redis基本操作..."
    
    local redis_cmd=$(build_redis_cmd)
    local test_key="health_check_$(date +%s)"
    local test_value="test_value_$(date +%s)"
    
    # 测试SET操作
    if timeout "$TIMEOUT" $redis_cmd set "$test_key" "$test_value" EX 60 2>/dev/null | grep -q "OK"; then
        log_success "Redis SET操作正常"
    else
        log_error "Redis SET操作失败"
        return 1
    fi
    
    # 测试GET操作
    if timeout "$TIMEOUT" $redis_cmd get "$test_key" 2>/dev/null | grep -q "$test_value"; then
        log_success "Redis GET操作正常"
    else
        log_error "Redis GET操作失败"
        return 1
    fi
    
    # 清理测试数据
    timeout "$TIMEOUT" $redis_cmd del "$test_key" >/dev/null 2>&1
    
    log_success "Redis基本操作测试通过"
    return 0
}

# 主健康检查函数
main_health_check() {
    local failed_checks=0
    local total_checks=0
    
    log_info "开始DL引擎Redis健康检查..."
    log_info "目标服务: $REDIS_HOST:$REDIS_PORT"
    log_info "超时时间: ${TIMEOUT}秒"
    echo "========================================"
    
    # 执行各项检查
    ((total_checks++))
    check_redis_process || ((failed_checks++))
    
    ((total_checks++))
    check_redis_port || ((failed_checks++))
    
    ((total_checks++))
    check_redis_connection || ((failed_checks++))
    
    ((total_checks++))
    check_redis_info || ((failed_checks++))
    
    ((total_checks++))
    check_redis_memory || ((failed_checks++))
    
    ((total_checks++))
    check_redis_clients || ((failed_checks++))
    
    ((total_checks++))
    check_redis_persistence || ((failed_checks++))
    
    ((total_checks++))
    check_redis_databases || ((failed_checks++))
    
    ((total_checks++))
    test_redis_operations || ((failed_checks++))
    
    echo "========================================"
    
    # 输出结果
    if [ $failed_checks -eq 0 ]; then
        log_success "所有健康检查通过 ($total_checks/$total_checks) ✓"
        exit 0
    else
        log_error "$failed_checks/$total_checks 项健康检查失败 ✗"
        exit 1
    fi
}

# 简单健康检查（用于Docker HEALTHCHECK）
simple_health_check() {
    local redis_cmd=$(build_redis_cmd)
    
    if timeout "$TIMEOUT" $redis_cmd ping 2>/dev/null | grep -q "PONG"; then
        exit 0
    else
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "DL引擎Redis健康检查脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  simple                  简单健康检查（仅连接测试）"
    echo "  full                    完整健康检查（默认）"
    echo "  help                    显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  REDIS_HOST              Redis主机地址 (默认: 127.0.0.1)"
    echo "  REDIS_PORT              Redis端口 (默认: 6379)"
    echo "  REDIS_PASSWORD          Redis密码"
    echo "  HEALTH_CHECK_TIMEOUT    超时时间 (默认: 5秒)"
    echo ""
    echo "示例:"
    echo "  $0                      # 执行完整健康检查"
    echo "  $0 simple               # 执行简单健康检查"
    echo "  REDIS_HOST=redis $0     # 检查远程Redis"
}

# 处理命令行参数
case "${1:-}" in
    "simple")
        simple_health_check
        ;;
    "full"|"")
        main_health_check
        ;;
    "help"|"-h"|"--help")
        show_help
        exit 0
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
