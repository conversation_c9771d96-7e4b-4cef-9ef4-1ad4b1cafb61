-- 监控服务数据库表结构初始化
USE monitoring;

-- 指标表
CREATE TABLE IF NOT EXISTS metrics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT '指标名称',
  value DECIMAL(10,4) NOT NULL COMMENT '指标值',
  service VARCHAR(50) COMMENT '服务名称',
  instance VARCHAR(50) COMMENT '实例标识',
  labels JSON COMMENT '标签信息',
  type ENUM('gauge', 'counter', 'histogram', 'summary') DEFAULT 'gauge' COMMENT '指标类型',
  description VARCHAR(500) COMMENT '指标描述',
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  INDEX idx_name_timestamp (name, timestamp),
  INDEX idx_service_timestamp (service, timestamp),
  INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统指标表';

-- 服务状态表
CREATE TABLE IF NOT EXISTS service_status (
  id INT AUTO_INCREMENT PRIMARY KEY,
  serviceName VARCHAR(100) NOT NULL UNIQUE COMMENT '服务名称',
  serviceUrl VARCHAR(200) COMMENT '服务URL',
  status ENUM('healthy', 'unhealthy', 'degraded', 'unknown') DEFAULT 'unknown' COMMENT '服务状态',
  responseTime INT COMMENT '响应时间(ms)',
  lastError TEXT COMMENT '最后错误信息',
  consecutiveFailures INT DEFAULT 0 COMMENT '连续失败次数',
  lastCheckAt TIMESTAMP COMMENT '最后检查时间',
  lastHealthyAt TIMESTAMP COMMENT '最后健康时间',
  metadata JSON COMMENT '元数据',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_service_updated (serviceName, updatedAt),
  INDEX idx_status (status),
  INDEX idx_last_check (lastCheckAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务状态表';

-- 健康检查表
CREATE TABLE IF NOT EXISTS health_checks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  serviceName VARCHAR(100) NOT NULL COMMENT '服务名称',
  checkType ENUM('http', 'tcp', 'database', 'redis', 'elasticsearch', 'custom') NOT NULL COMMENT '检查类型',
  status ENUM('pass', 'fail', 'warn') NOT NULL COMMENT '检查状态',
  responseTime INT COMMENT '响应时间(ms)',
  message TEXT COMMENT '检查消息',
  error TEXT COMMENT '错误信息',
  details JSON COMMENT '详细信息',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX idx_service_type_created (serviceName, checkType, createdAt),
  INDEX idx_status_created (status, createdAt),
  INDEX idx_created (createdAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康检查表';

-- 告警规则表
CREATE TABLE IF NOT EXISTS alert_rules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE COMMENT '规则名称',
  description TEXT COMMENT '规则描述',
  metric VARCHAR(100) NOT NULL COMMENT '监控指标',
  operator ENUM('>', '<', '>=', '<=', '=', '!=') NOT NULL COMMENT '比较操作符',
  threshold DECIMAL(10,4) NOT NULL COMMENT '阈值',
  duration INT DEFAULT 300 COMMENT '持续时间(秒)',
  severity ENUM('info', 'warning', 'error', 'critical') DEFAULT 'warning' COMMENT '告警级别',
  enabled BOOLEAN DEFAULT true COMMENT '是否启用',
  labels JSON COMMENT '标签',
  annotations JSON COMMENT '注释',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_metric (metric),
  INDEX idx_enabled (enabled),
  INDEX idx_severity (severity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警规则表';

-- 告警表
CREATE TABLE IF NOT EXISTS alerts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  ruleId INT NOT NULL COMMENT '规则ID',
  status ENUM('firing', 'resolved') DEFAULT 'firing' COMMENT '告警状态',
  startsAt TIMESTAMP NOT NULL COMMENT '开始时间',
  endsAt TIMESTAMP COMMENT '结束时间',
  labels JSON COMMENT '标签',
  annotations JSON COMMENT '注释',
  fingerprint VARCHAR(64) NOT NULL UNIQUE COMMENT '指纹',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (ruleId) REFERENCES alert_rules(id) ON DELETE CASCADE,
  INDEX idx_rule_status (ruleId, status),
  INDEX idx_fingerprint (fingerprint),
  INDEX idx_status_starts (status, startsAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警表';

-- 日志条目表
CREATE TABLE IF NOT EXISTS log_entries (
  id INT AUTO_INCREMENT PRIMARY KEY,
  level ENUM('error', 'warn', 'info', 'debug', 'verbose') NOT NULL COMMENT '日志级别',
  message TEXT NOT NULL COMMENT '日志消息',
  service VARCHAR(100) COMMENT '服务名称',
  module VARCHAR(100) COMMENT '模块名称',
  requestId VARCHAR(50) COMMENT '请求ID',
  userId VARCHAR(50) COMMENT '用户ID',
  context JSON COMMENT '上下文信息',
  stack TEXT COMMENT '堆栈信息',
  ip VARCHAR(45) COMMENT 'IP地址',
  userAgent VARCHAR(500) COMMENT '用户代理',
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  INDEX idx_level_timestamp (level, timestamp),
  INDEX idx_service_timestamp (service, timestamp),
  INDEX idx_timestamp (timestamp),
  INDEX idx_request_id (requestId)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日志条目表';

-- 通知表
CREATE TABLE IF NOT EXISTS notifications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  type ENUM('email', 'dingtalk', 'wechat', 'slack', 'webhook', 'sms') NOT NULL COMMENT '通知类型',
  status ENUM('pending', 'sent', 'failed', 'retry') DEFAULT 'pending' COMMENT '通知状态',
  alertId INT COMMENT '告警ID',
  title VARCHAR(200) NOT NULL COMMENT '通知标题',
  content TEXT NOT NULL COMMENT '通知内容',
  recipients JSON NOT NULL COMMENT '接收者列表',
  config JSON COMMENT '配置信息',
  retryCount INT DEFAULT 0 COMMENT '重试次数',
  maxRetries INT DEFAULT 3 COMMENT '最大重试次数',
  error TEXT COMMENT '错误信息',
  sentAt TIMESTAMP COMMENT '发送时间',
  nextRetryAt TIMESTAMP COMMENT '下次重试时间',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_type_status_created (type, status, createdAt),
  INDEX idx_alert (alertId),
  INDEX idx_status_retry (status, nextRetryAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- 通知模板表
CREATE TABLE IF NOT EXISTS notification_templates (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE COMMENT '模板名称',
  type ENUM('email', 'dingtalk', 'wechat', 'slack', 'webhook', 'sms') NOT NULL COMMENT '通知类型',
  title VARCHAR(200) NOT NULL COMMENT '模板标题',
  content TEXT NOT NULL COMMENT '模板内容',
  variables JSON COMMENT '变量列表',
  description TEXT COMMENT '模板描述',
  enabled BOOLEAN DEFAULT true COMMENT '是否启用',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_type_enabled (type, enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知模板表';

-- 插入默认通知模板
INSERT INTO notification_templates (name, type, title, content, variables, description) VALUES
('alert_email', 'email', '系统告警: {{alertName}}', 
 '告警名称: {{alertName}}\n告警级别: {{severity}}\n告警时间: {{startsAt}}\n告警描述: {{description}}\n当前值: {{value}}\n阈值: {{threshold}}',
 '["alertName", "severity", "startsAt", "description", "value", "threshold"]',
 '默认邮件告警模板'),
('alert_dingtalk', 'dingtalk', '系统告警',
 '## 系统告警通知\n\n**告警名称**: {{alertName}}\n\n**告警级别**: {{severity}}\n\n**告警时间**: {{startsAt}}\n\n**告警描述**: {{description}}\n\n**当前值**: {{value}}\n\n**阈值**: {{threshold}}',
 '["alertName", "severity", "startsAt", "description", "value", "threshold"]',
 '默认钉钉告警模板');

-- 创建清理索引
CREATE INDEX idx_metrics_cleanup ON metrics(timestamp);
CREATE INDEX idx_logs_cleanup ON log_entries(timestamp);
CREATE INDEX idx_health_checks_cleanup ON health_checks(createdAt);

-- 输出初始化信息
SELECT 'monitoring数据库表结构初始化完成' AS status,
       COUNT(*) AS table_count
FROM information_schema.tables 
WHERE table_schema = 'monitoring';
