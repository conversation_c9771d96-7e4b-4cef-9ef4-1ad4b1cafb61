version: '3.8'

services:
  # 监控服务
  monitoring-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: monitoring-service-dev
    restart: unless-stopped
    ports:
      - "3100:3100"
    environment:
      - NODE_ENV=development
      - PORT=3100
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=monitoring_password
      - DB_DATABASE=monitoring
      - DB_SYNCHRONIZE=true
      - ELASTICSEARCH_ENABLED=true
      - ELASTICSEARCH_NODE=http://elasticsearch:9200
      - ELASTICSEARCH_INDEX=logs
      - LOG_LEVEL=debug
    volumes:
      - ./logs:/app/logs
      - ./src:/app/src
    depends_on:
      mysql:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    networks:
      - monitoring-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3100/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: monitoring-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: monitoring_password
      MYSQL_DATABASE: monitoring
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/database/migrations:/docker-entrypoint-initdb.d
    networks:
      - monitoring-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Elasticsearch
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: monitoring-elasticsearch-dev
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - monitoring-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Kibana (可选)
  kibana:
    image: kibana:8.11.0
    container_name: monitoring-kibana-dev
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - monitoring-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis (用于缓存和会话)
  redis:
    image: redis:7.0-alpine
    container_name: monitoring-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - monitoring-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Prometheus (可选，用于指标收集)
  prometheus:
    image: prom/prometheus:latest
    container_name: monitoring-prometheus-dev
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring-network

  # Grafana (可选，用于可视化)
  grafana:
    image: grafana/grafana:latest
    container_name: monitoring-grafana-dev
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - monitoring-network

volumes:
  mysql_data:
    driver: local
  elasticsearch_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  monitoring-network:
    driver: bridge
