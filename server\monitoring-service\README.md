# 监控服务 (Monitoring Service)

DL引擎系统的监控服务，负责系统监控、告警、日志分析和通知功能。

## 功能特性

### 🔍 系统监控
- **服务健康检查**: 自动检测各微服务的健康状态
- **性能指标收集**: CPU、内存、磁盘、网络等系统指标
- **Prometheus集成**: 提供标准的Prometheus指标端点
- **自动恢复**: 检测到服务异常时自动尝试恢复

### 🚨 告警系统
- **规则引擎**: 灵活的告警规则配置
- **多级告警**: 支持警告、错误、严重等多个级别
- **告警抑制**: 避免重复告警的智能抑制机制
- **告警历史**: 完整的告警记录和统计

### 📊 日志分析
- **日志聚合**: 收集和聚合各服务的日志
- **Elasticsearch集成**: 强大的日志搜索和分析
- **日志分级**: 支持不同级别的日志过滤
- **实时监控**: 实时日志流监控

### 📢 通知系统
- **多渠道通知**: 邮件、钉钉、企业微信、Slack等
- **通知模板**: 可自定义的通知消息模板
- **通知策略**: 灵活的通知频率和策略配置
- **通知历史**: 完整的通知发送记录

## 技术架构

### 核心技术栈
- **框架**: NestJS + TypeScript
- **数据库**: MySQL (存储监控数据和配置)
- **搜索引擎**: Elasticsearch (日志存储和分析)
- **指标系统**: Prometheus + prom-client
- **任务调度**: @nestjs/schedule
- **邮件服务**: Nodemailer

### 模块结构
```
src/
├── monitoring/          # 监控模块
│   ├── metrics-collector.service.ts    # 指标收集
│   ├── metrics-aggregator.service.ts   # 指标聚合
│   ├── prometheus-exporter.service.ts  # Prometheus导出
│   └── monitoring.controller.ts        # 监控API
├── alert/              # 告警模块
│   ├── alert-rule.service.ts          # 告警规则
│   ├── alert-evaluator.service.ts     # 告警评估
│   └── alert.controller.ts            # 告警API
├── health/             # 健康检查模块
│   ├── service-health-check.service.ts # 服务健康检查
│   ├── auto-recovery.service.ts        # 自动恢复
│   └── health.controller.ts            # 健康检查API
├── logging/            # 日志模块
│   ├── log-aggregator.service.ts      # 日志聚合
│   ├── elasticsearch-log.service.ts   # ES日志服务
│   └── logging.controller.ts          # 日志API
└── notification/       # 通知模块
    ├── notifiers/                      # 通知器
    │   ├── email-notifier.service.ts   # 邮件通知
    │   ├── dingtalk-notifier.service.ts # 钉钉通知
    │   ├── wechat-notifier.service.ts   # 企业微信通知
    │   └── slack-notifier.service.ts    # Slack通知
    └── notification.controller.ts      # 通知API
```

## 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0+
- Elasticsearch 8.0+ (可选)

### 安装依赖
```bash
npm install
```

### 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和其他服务连接信息
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## API 文档

### 健康检查
```http
GET /api/v1/health
```

### 监控指标
```http
GET /metrics                    # Prometheus指标
GET /api/v1/monitoring/metrics  # 系统指标
GET /api/v1/monitoring/services # 服务状态
```

### 告警管理
```http
GET    /api/v1/alerts           # 获取告警列表
POST   /api/v1/alerts           # 创建告警规则
PUT    /api/v1/alerts/:id       # 更新告警规则
DELETE /api/v1/alerts/:id       # 删除告警规则
```

### 日志查询
```http
GET /api/v1/logs                # 查询日志
GET /api/v1/logs/search         # 搜索日志
GET /api/v1/logs/stats          # 日志统计
```

### 通知管理
```http
GET  /api/v1/notifications      # 获取通知历史
POST /api/v1/notifications/test # 测试通知
```

## 配置说明

### 告警规则配置
```typescript
{
  name: "CPU使用率过高",
  metric: "cpu_usage_percent",
  operator: ">",
  threshold: 80,
  duration: 300,  // 持续时间(秒)
  severity: "warning",
  enabled: true
}
```

### 通知配置
```typescript
{
  type: "email",
  config: {
    to: ["<EMAIL>"],
    subject: "系统告警",
    template: "alert_template"
  }
}
```

## Docker 部署

### 构建镜像
```bash
docker build -t monitoring-service .
```

### 运行容器
```bash
docker run -d \
  --name monitoring-service \
  -p 3100:3100 \
  -e DB_HOST=mysql \
  -e DB_PASSWORD=your_password \
  monitoring-service
```

## 监控指标

### 系统指标
- `system_cpu_usage_percent`: CPU使用率
- `system_memory_usage_percent`: 内存使用率
- `system_disk_usage_percent`: 磁盘使用率
- `system_load_average`: 系统负载

### 服务指标
- `service_health_status`: 服务健康状态
- `service_response_time`: 服务响应时间
- `service_error_rate`: 服务错误率
- `service_request_count`: 服务请求数

### 告警指标
- `alert_total_count`: 告警总数
- `alert_active_count`: 活跃告警数
- `alert_resolved_count`: 已解决告警数

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库配置
   echo $DB_HOST $DB_PORT $DB_USERNAME
   
   # 测试数据库连接
   mysql -h $DB_HOST -P $DB_PORT -u $DB_USERNAME -p
   ```

2. **Elasticsearch连接失败**
   ```bash
   # 检查ES服务状态
   curl http://localhost:9200/_cluster/health
   
   # 检查ES配置
   echo $ELASTICSEARCH_NODE
   ```

3. **邮件发送失败**
   ```bash
   # 检查邮件配置
   echo $MAIL_HOST $MAIL_PORT $MAIL_USER
   
   # 测试邮件发送
   curl -X POST http://localhost:3100/api/v1/notifications/test
   ```

### 日志查看
```bash
# 查看应用日志
docker logs monitoring-service

# 查看详细日志
docker logs -f monitoring-service
```

## 开发指南

### 添加新的监控指标
1. 在 `metrics-collector.service.ts` 中添加收集逻辑
2. 在 `prometheus-exporter.service.ts` 中注册指标
3. 更新相关的类型定义

### 添加新的通知渠道
1. 在 `notification/notifiers/` 目录下创建新的通知器
2. 实现 `NotifierInterface` 接口
3. 在 `notification.service.ts` 中注册新通知器

### 添加新的告警规则
1. 在 `alert/entities/` 中定义规则实体
2. 在 `alert-evaluator.service.ts` 中实现评估逻辑
3. 更新告警控制器和API

## 许可证

MIT License
