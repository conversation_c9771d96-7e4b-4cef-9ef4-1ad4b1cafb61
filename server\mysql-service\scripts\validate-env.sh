#!/bin/bash

# DL引擎MySQL环境变量验证脚本
# 确保所有必需的环境变量都已正确设置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${BLUE}INFO${NC}: $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS${NC}: $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING${NC}: $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR${NC}: $1"
}

# 验证必需的环境变量
validate_required_vars() {
    log_info "验证必需的环境变量..."
    
    local missing_vars=()
    
    # 检查MySQL root密码
    if [ -z "$MYSQL_ROOT_PASSWORD" ]; then
        missing_vars+=("MYSQL_ROOT_PASSWORD")
    fi
    
    # 检查密码强度
    if [ -n "$MYSQL_ROOT_PASSWORD" ]; then
        local password_length=${#MYSQL_ROOT_PASSWORD}
        if [ $password_length -lt 8 ]; then
            log_warning "MySQL root密码长度少于8位，建议使用更强的密码"
        fi
        
        # 检查是否使用默认密码
        if [ "$MYSQL_ROOT_PASSWORD" = "dl_engine_password_2024" ] || [ "$MYSQL_ROOT_PASSWORD" = "your_secure_password_here" ]; then
            log_warning "检测到使用默认密码，强烈建议修改为自定义安全密码"
        fi
    fi
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "缺少必需的环境变量:"
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        log_error "请在.env文件中设置这些变量"
        return 1
    fi
    
    log_success "所有必需的环境变量验证通过"
    return 0
}

# 验证可选环境变量
validate_optional_vars() {
    log_info "验证可选环境变量..."
    
    # 检查端口配置
    if [ -n "$MYSQL_PORT" ]; then
        if ! [[ "$MYSQL_PORT" =~ ^[0-9]+$ ]] || [ "$MYSQL_PORT" -lt 1024 ] || [ "$MYSQL_PORT" -gt 65535 ]; then
            log_warning "MySQL端口配置无效: $MYSQL_PORT，将使用默认端口3306"
        else
            log_info "MySQL端口: $MYSQL_PORT"
        fi
    fi
    
    # 检查数据目录
    if [ -n "$MYSQL_DATA_DIR" ]; then
        log_info "MySQL数据目录: $MYSQL_DATA_DIR"
    fi
    
    # 检查备份配置
    if [ -n "$BACKUP_RETENTION_DAYS" ]; then
        if ! [[ "$BACKUP_RETENTION_DAYS" =~ ^[0-9]+$ ]] || [ "$BACKUP_RETENTION_DAYS" -lt 1 ]; then
            log_warning "备份保留天数配置无效: $BACKUP_RETENTION_DAYS，将使用默认值7天"
        else
            log_info "备份保留天数: $BACKUP_RETENTION_DAYS"
        fi
    fi
    
    log_success "可选环境变量验证完成"
}

# 生成安全建议
generate_security_recommendations() {
    log_info "生成安全建议..."
    
    echo "========================================"
    echo "MySQL服务安全建议:"
    echo "========================================"
    
    # 密码安全建议
    if [ -n "$MYSQL_ROOT_PASSWORD" ]; then
        local password_length=${#MYSQL_ROOT_PASSWORD}
        if [ $password_length -lt 12 ]; then
            echo "1. 建议使用至少12位的复杂密码"
        fi
        
        if [[ "$MYSQL_ROOT_PASSWORD" =~ ^[a-zA-Z0-9]*$ ]]; then
            echo "2. 建议密码包含特殊字符（如!@#$%^&*）"
        fi
    fi
    
    # 网络安全建议
    echo "3. 生产环境建议限制MySQL网络访问"
    echo "4. 建议启用SSL连接"
    echo "5. 建议定期更换数据库密码"
    echo "6. 建议启用审计日志"
    echo "7. 建议定期备份数据库"
    
    echo "========================================"
}

# 显示环境变量摘要
show_env_summary() {
    log_info "环境变量配置摘要:"
    echo "========================================"
    echo "MySQL配置:"
    echo "  端口: ${MYSQL_PORT:-3306}"
    echo "  数据目录: ${MYSQL_DATA_DIR:-./data/mysql}"
    echo "  备份目录: ${MYSQL_BACKUP_DIR:-./data/backups}"
    echo "  日志目录: ${MYSQL_LOG_DIR:-./data/logs}"
    echo "  备份保留: ${BACKUP_RETENTION_DAYS:-7}天"
    echo ""
    echo "phpMyAdmin配置:"
    echo "  端口: ${PHPMYADMIN_PORT:-8080}"
    echo "  启用状态: ${ENABLE_PHPMYADMIN:-true}"
    echo ""
    echo "安全配置:"
    echo "  密码已设置: $([ -n "$MYSQL_ROOT_PASSWORD" ] && echo "是" || echo "否")"
    echo "  SSL启用: ${ENABLE_SSL:-false}"
    echo "========================================"
}

# 主验证函数
main() {
    log_info "开始DL引擎MySQL环境变量验证..."
    
    # 验证必需变量
    if ! validate_required_vars; then
        log_error "环境变量验证失败"
        exit 1
    fi
    
    # 验证可选变量
    validate_optional_vars
    
    # 显示配置摘要
    show_env_summary
    
    # 生成安全建议
    generate_security_recommendations
    
    log_success "环境变量验证完成"
}

# 显示帮助信息
show_help() {
    echo "DL引擎MySQL环境变量验证脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  validate                验证环境变量（默认）"
    echo "  help                    显示帮助信息"
    echo ""
    echo "必需的环境变量:"
    echo "  MYSQL_ROOT_PASSWORD     MySQL root密码"
    echo ""
    echo "可选的环境变量:"
    echo "  MYSQL_PORT              MySQL端口 (默认: 3306)"
    echo "  MYSQL_DATA_DIR          数据目录"
    echo "  MYSQL_BACKUP_DIR        备份目录"
    echo "  BACKUP_RETENTION_DAYS   备份保留天数 (默认: 7)"
    echo ""
    echo "示例:"
    echo "  $0                      # 验证环境变量"
    echo "  $0 validate             # 验证环境变量"
}

# 处理命令行参数
case "${1:-}" in
    "validate"|"")
        main
        ;;
    "help"|"-h"|"--help")
        show_help
        exit 0
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
